<?xml version="1.0" encoding="us-ascii"?>
<MQSim_IO_Scenarios>
	<IO_Scenario>
		<IO_Flow_Parameter_Set_Synthetic>
				 <Priority_Class>HIGH</Priority_Class>
				 <Device_Level_Data_Caching_Mode>WRITE_CACHE</Device_Level_Data_Caching_Mode>
				 <Channel_IDs>0,1,2,3</Channel_IDs>
				 <Chip_IDs>0,1,2,3</Chip_IDs>
				 <Die_IDs>0,1</Die_IDs>
				 <Plane_IDs>0,1</Plane_IDs>
				 <Initial_Occupancy_Percentage>75</Initial_Occupancy_Percentage>
				 <Working_Set_Percentage>1</Working_Set_Percentage>
				 <Synthetic_Generator_Type>QUEUE_DEPTH</Synthetic_Generator_Type>
				 <Read_Percentage>0</Read_Percentage>
				 <Address_Distribution>RANDOM_HOTCOLD</Address_Distribution>
				 <Percentage_of_Hot_Region>1</Percentage_of_Hot_Region>
				 <Generated_Aligned_Addresses>true</Generated_Aligned_Addresses>
				 <Address_Alignment_Unit>16</Address_Alignment_Unit>
				 <Request_Size_Distribution>FIXED</Request_Size_Distribution>
				 <Average_Request_Size>8</Average_Request_Size>
				 <Variance_Request_Size>0</Variance_Request_Size>
				 <Seed>798</Seed>
				 <Average_No_of_Reqs_in_Queue>8</Average_No_of_Reqs_in_Queue>
				 <Stop_Time>10000000000</Stop_Time>
				 <Total_Requests_To_Generate>0</Total_Requests_To_Generate>
		</IO_Flow_Parameter_Set_Synthetic>
		<IO_Flow_Parameter_Set_Synthetic>
				 <Priority_Class>HIGH</Priority_Class>
				 <Device_Level_Data_Caching_Mode>WRITE_CACHE</Device_Level_Data_Caching_Mode>
				 <Channel_IDs>4,5,6,7</Channel_IDs>
				 <Chip_IDs>0,1,2,3</Chip_IDs>
				 <Die_IDs>0,1</Die_IDs>
				 <Plane_IDs>0,1</Plane_IDs>
				 <Initial_Occupancy_Percentage>77</Initial_Occupancy_Percentage>
				 <Working_Set_Percentage>1</Working_Set_Percentage>
				 <Synthetic_Generator_Type>QUEUE_DEPTH</Synthetic_Generator_Type>
				 <Read_Percentage>0</Read_Percentage>
				 <Address_Distribution>RANDOM_HOTCOLD</Address_Distribution>
				 <Percentage_of_Hot_Region>1</Percentage_of_Hot_Region>
				 <Generated_Aligned_Addresses>true</Generated_Aligned_Addresses>
				 <Address_Alignment_Unit>16</Address_Alignment_Unit>
				 <Request_Size_Distribution>FIXED</Request_Size_Distribution>
				 <Average_Request_Size>8</Average_Request_Size>
				 <Variance_Request_Size>0</Variance_Request_Size>
				 <Seed>798</Seed>
				 <Average_No_of_Reqs_in_Queue>8</Average_No_of_Reqs_in_Queue>
				 <Stop_Time>10000000000</Stop_Time>
				 <Total_Requests_To_Generate>0</Total_Requests_To_Generate>
		</IO_Flow_Parameter_Set_Synthetic>
	</IO_Scenario>
	<IO_Scenario>
		<IO_Flow_Parameter_Set_Synthetic>
				 <Priority_Class>HIGH</Priority_Class>
				 <Device_Level_Data_Caching_Mode>WRITE_CACHE</Device_Level_Data_Caching_Mode>
				 <Channel_IDs>0,1,2,3</Channel_IDs>
				 <Chip_IDs>0,1,2,3</Chip_IDs>
				 <Die_IDs>0,1</Die_IDs>
				 <Plane_IDs>0,1</Plane_IDs>
				 <Initial_Occupancy_Percentage>75</Initial_Occupancy_Percentage>
				 <Working_Set_Percentage>1</Working_Set_Percentage>
				 <Synthetic_Generator_Type>QUEUE_DEPTH</Synthetic_Generator_Type>
				 <Read_Percentage>0</Read_Percentage>
				 <Address_Distribution>RANDOM_HOTCOLD</Address_Distribution>
				 <Percentage_of_Hot_Region>1</Percentage_of_Hot_Region>
				 <Generated_Aligned_Addresses>true</Generated_Aligned_Addresses>
				 <Address_Alignment_Unit>16</Address_Alignment_Unit>
				 <Request_Size_Distribution>FIXED</Request_Size_Distribution>
				 <Average_Request_Size>8</Average_Request_Size>
				 <Variance_Request_Size>0</Variance_Request_Size>
				 <Seed>798</Seed>
				 <Average_No_of_Reqs_in_Queue>8</Average_No_of_Reqs_in_Queue>
				 <Stop_Time>100000000000</Stop_Time>
				 <Total_Requests_To_Generate>0</Total_Requests_To_Generate>
		</IO_Flow_Parameter_Set_Synthetic>
		<IO_Flow_Parameter_Set_Synthetic>
				 <Priority_Class>HIGH</Priority_Class>
				 <Device_Level_Data_Caching_Mode>WRITE_CACHE</Device_Level_Data_Caching_Mode>
				 <Channel_IDs>4,5,6,7</Channel_IDs>
				 <Chip_IDs>0,1,2,3</Chip_IDs>
				 <Die_IDs>0,1</Die_IDs>
				 <Plane_IDs>0,1</Plane_IDs>
				 <Initial_Occupancy_Percentage>77</Initial_Occupancy_Percentage>
				 <Working_Set_Percentage>1</Working_Set_Percentage>
				 <Synthetic_Generator_Type>QUEUE_DEPTH</Synthetic_Generator_Type>
				 <Read_Percentage>0</Read_Percentage>
				 <Address_Distribution>RANDOM_HOTCOLD</Address_Distribution>
				 <Percentage_of_Hot_Region>1</Percentage_of_Hot_Region>
				 <Generated_Aligned_Addresses>true</Generated_Aligned_Addresses>
				 <Address_Alignment_Unit>16</Address_Alignment_Unit>
				 <Request_Size_Distribution>FIXED</Request_Size_Distribution>
				 <Average_Request_Size>8</Average_Request_Size>
				 <Variance_Request_Size>0</Variance_Request_Size>
				 <Seed>798</Seed>
				 <Average_No_of_Reqs_in_Queue>16</Average_No_of_Reqs_in_Queue>
				 <Stop_Time>100000000000</Stop_Time>
				 <Total_Requests_To_Generate>0</Total_Requests_To_Generate>
		</IO_Flow_Parameter_Set_Synthetic>
	</IO_Scenario>
	<IO_Scenario>
		<IO_Flow_Parameter_Set_Synthetic>
				 <Priority_Class>HIGH</Priority_Class>
				 <Device_Level_Data_Caching_Mode>WRITE_CACHE</Device_Level_Data_Caching_Mode>
				 <Channel_IDs>0,1,2,3</Channel_IDs>
				 <Chip_IDs>0,1,2,3</Chip_IDs>
				 <Die_IDs>0,1</Die_IDs>
				 <Plane_IDs>0,1</Plane_IDs>
				 <Initial_Occupancy_Percentage>75</Initial_Occupancy_Percentage>
				 <Working_Set_Percentage>1</Working_Set_Percentage>
				 <Synthetic_Generator_Type>QUEUE_DEPTH</Synthetic_Generator_Type>
				 <Read_Percentage>0</Read_Percentage>
				 <Address_Distribution>RANDOM_HOTCOLD</Address_Distribution>
				 <Percentage_of_Hot_Region>1</Percentage_of_Hot_Region>
				 <Generated_Aligned_Addresses>true</Generated_Aligned_Addresses>
				 <Address_Alignment_Unit>16</Address_Alignment_Unit>
				 <Request_Size_Distribution>FIXED</Request_Size_Distribution>
				 <Average_Request_Size>8</Average_Request_Size>
				 <Variance_Request_Size>0</Variance_Request_Size>
				 <Seed>798</Seed>
				 <Average_No_of_Reqs_in_Queue>8</Average_No_of_Reqs_in_Queue>
				 <Stop_Time>100000000000</Stop_Time>
				 <Total_Requests_To_Generate>0</Total_Requests_To_Generate>
		</IO_Flow_Parameter_Set_Synthetic>
		<IO_Flow_Parameter_Set_Synthetic>
				 <Priority_Class>HIGH</Priority_Class>
				 <Device_Level_Data_Caching_Mode>WRITE_CACHE</Device_Level_Data_Caching_Mode>
				 <Channel_IDs>4,5,6,7</Channel_IDs>
				 <Chip_IDs>0,1,2,3</Chip_IDs>
				 <Die_IDs>0,1</Die_IDs>
				 <Plane_IDs>0,1</Plane_IDs>
				 <Initial_Occupancy_Percentage>87</Initial_Occupancy_Percentage>
				 <Working_Set_Percentage>100</Working_Set_Percentage>
				 <Synthetic_Generator_Type>QUEUE_DEPTH</Synthetic_Generator_Type>
				 <Read_Percentage>0</Read_Percentage>
				 <Address_Distribution>RANDOM_HOTCOLD</Address_Distribution>
				 <Percentage_of_Hot_Region>50</Percentage_of_Hot_Region>
				 <Generated_Aligned_Addresses>true</Generated_Aligned_Addresses>
				 <Address_Alignment_Unit>16</Address_Alignment_Unit>
				 <Request_Size_Distribution>FIXED</Request_Size_Distribution>
				 <Average_Request_Size>8</Average_Request_Size>
				 <Variance_Request_Size>0</Variance_Request_Size>
				 <Seed>798</Seed>
				 <Average_No_of_Reqs_in_Queue>32</Average_No_of_Reqs_in_Queue>
				 <Stop_Time>100000000000</Stop_Time>
				 <Total_Requests_To_Generate>0</Total_Requests_To_Generate>
		</IO_Flow_Parameter_Set_Synthetic>
	</IO_Scenario>
	<IO_Scenario>
		<IO_Flow_Parameter_Set_Synthetic>
				 <Priority_Class>HIGH</Priority_Class>
				 <Device_Level_Data_Caching_Mode>WRITE_CACHE</Device_Level_Data_Caching_Mode>
				 <Channel_IDs>0,1,2,3</Channel_IDs>
				 <Chip_IDs>0,1,2,3</Chip_IDs>
				 <Die_IDs>0,1</Die_IDs>
				 <Plane_IDs>0,1</Plane_IDs>
				 <Initial_Occupancy_Percentage>75</Initial_Occupancy_Percentage>
				 <Working_Set_Percentage>1</Working_Set_Percentage>
				 <Synthetic_Generator_Type>QUEUE_DEPTH</Synthetic_Generator_Type>
				 <Read_Percentage>0</Read_Percentage>
				 <Address_Distribution>RANDOM_HOTCOLD</Address_Distribution>
				 <Percentage_of_Hot_Region>1</Percentage_of_Hot_Region>
				 <Generated_Aligned_Addresses>true</Generated_Aligned_Addresses>
				 <Address_Alignment_Unit>16</Address_Alignment_Unit>
				 <Request_Size_Distribution>FIXED</Request_Size_Distribution>
				 <Average_Request_Size>8</Average_Request_Size>
				 <Variance_Request_Size>0</Variance_Request_Size>
				 <Seed>798</Seed>
				 <Average_No_of_Reqs_in_Queue>8</Average_No_of_Reqs_in_Queue>
				 <Stop_Time>100000000000</Stop_Time>
				 <Total_Requests_To_Generate>0</Total_Requests_To_Generate>
		</IO_Flow_Parameter_Set_Synthetic>
		<IO_Flow_Parameter_Set_Synthetic>
				 <Priority_Class>HIGH</Priority_Class>
				 <Device_Level_Data_Caching_Mode>WRITE_CACHE</Device_Level_Data_Caching_Mode>
				 <Channel_IDs>4,5,6,7</Channel_IDs>
				 <Chip_IDs>0,1,2,3</Chip_IDs>
				 <Die_IDs>0,1</Die_IDs>
				 <Plane_IDs>0,1</Plane_IDs>
				 <Initial_Occupancy_Percentage>88</Initial_Occupancy_Percentage>
				 <Working_Set_Percentage>100</Working_Set_Percentage>
				 <Synthetic_Generator_Type>QUEUE_DEPTH</Synthetic_Generator_Type>
				 <Read_Percentage>0</Read_Percentage>
				 <Address_Distribution>RANDOM_HOTCOLD</Address_Distribution>
				 <Percentage_of_Hot_Region>50</Percentage_of_Hot_Region>
				 <Generated_Aligned_Addresses>true</Generated_Aligned_Addresses>
				 <Address_Alignment_Unit>16</Address_Alignment_Unit>
				 <Request_Size_Distribution>FIXED</Request_Size_Distribution>
				 <Average_Request_Size>8</Average_Request_Size>
				 <Variance_Request_Size>0</Variance_Request_Size>
				 <Seed>798</Seed>
				 <Average_No_of_Reqs_in_Queue>64</Average_No_of_Reqs_in_Queue>
				 <Stop_Time>100000000000</Stop_Time>
				 <Total_Requests_To_Generate>0</Total_Requests_To_Generate>
		</IO_Flow_Parameter_Set_Synthetic>
	</IO_Scenario>
	<IO_Scenario>
		<IO_Flow_Parameter_Set_Synthetic>
				 <Priority_Class>HIGH</Priority_Class>
				 <Device_Level_Data_Caching_Mode>WRITE_CACHE</Device_Level_Data_Caching_Mode>
				 <Channel_IDs>0,1,2,3</Channel_IDs>
				 <Chip_IDs>0,1,2,3</Chip_IDs>
				 <Die_IDs>0,1</Die_IDs>
				 <Plane_IDs>0,1</Plane_IDs>
				 <Initial_Occupancy_Percentage>85</Initial_Occupancy_Percentage>
				 <Working_Set_Percentage>1</Working_Set_Percentage>
				 <Synthetic_Generator_Type>QUEUE_DEPTH</Synthetic_Generator_Type>
				 <Read_Percentage>0</Read_Percentage>
				 <Address_Distribution>RANDOM_HOTCOLD</Address_Distribution>
				 <Percentage_of_Hot_Region>1</Percentage_of_Hot_Region>
				 <Generated_Aligned_Addresses>true</Generated_Aligned_Addresses>
				 <Address_Alignment_Unit>16</Address_Alignment_Unit>
				 <Request_Size_Distribution>FIXED</Request_Size_Distribution>
				 <Average_Request_Size>8</Average_Request_Size>
				 <Variance_Request_Size>0</Variance_Request_Size>
				 <Seed>798</Seed>
				 <Average_No_of_Reqs_in_Queue>8</Average_No_of_Reqs_in_Queue>
				 <Stop_Time>100000000000</Stop_Time>
				 <Total_Requests_To_Generate>0</Total_Requests_To_Generate>
		</IO_Flow_Parameter_Set_Synthetic>
		<IO_Flow_Parameter_Set_Synthetic>
				 <Priority_Class>HIGH</Priority_Class>
				 <Device_Level_Data_Caching_Mode>WRITE_CACHE</Device_Level_Data_Caching_Mode>
				 <Channel_IDs>4,5,6,7</Channel_IDs>
				 <Chip_IDs>0,1,2,3</Chip_IDs>
				 <Die_IDs>0,1</Die_IDs>
				 <Plane_IDs>0,1</Plane_IDs>
				 <Initial_Occupancy_Percentage>89</Initial_Occupancy_Percentage>
				 <Working_Set_Percentage>100</Working_Set_Percentage>
				 <Synthetic_Generator_Type>QUEUE_DEPTH</Synthetic_Generator_Type>
				 <Read_Percentage>0</Read_Percentage>
				 <Address_Distribution>RANDOM_HOTCOLD</Address_Distribution>
				 <Percentage_of_Hot_Region>50</Percentage_of_Hot_Region>
				 <Generated_Aligned_Addresses>true</Generated_Aligned_Addresses>
				 <Address_Alignment_Unit>16</Address_Alignment_Unit>
				 <Request_Size_Distribution>FIXED</Request_Size_Distribution>
				 <Average_Request_Size>8</Average_Request_Size>
				 <Variance_Request_Size>0</Variance_Request_Size>
				 <Seed>798</Seed>
				 <Average_No_of_Reqs_in_Queue>128</Average_No_of_Reqs_in_Queue>
				 <Stop_Time>100000000000</Stop_Time>
				 <Total_Requests_To_Generate>0</Total_Requests_To_Generate>
		</IO_Flow_Parameter_Set_Synthetic>
	</IO_Scenario>
	<IO_Scenario>
		<IO_Flow_Parameter_Set_Synthetic>
				 <Priority_Class>HIGH</Priority_Class>
				 <Device_Level_Data_Caching_Mode>WRITE_CACHE</Device_Level_Data_Caching_Mode>
				 <Channel_IDs>0,1,2,3</Channel_IDs>
				 <Chip_IDs>0,1,2,3</Chip_IDs>
				 <Die_IDs>0,1</Die_IDs>
				 <Plane_IDs>0,1</Plane_IDs>
				 <Initial_Occupancy_Percentage>75</Initial_Occupancy_Percentage>
				 <Working_Set_Percentage>1</Working_Set_Percentage>
				 <Synthetic_Generator_Type>QUEUE_DEPTH</Synthetic_Generator_Type>
				 <Read_Percentage>0</Read_Percentage>
				 <Address_Distribution>RANDOM_HOTCOLD</Address_Distribution>
				 <Percentage_of_Hot_Region>1</Percentage_of_Hot_Region>
				 <Generated_Aligned_Addresses>true</Generated_Aligned_Addresses>
				 <Address_Alignment_Unit>16</Address_Alignment_Unit>
				 <Request_Size_Distribution>FIXED</Request_Size_Distribution>
				 <Average_Request_Size>8</Average_Request_Size>
				 <Variance_Request_Size>0</Variance_Request_Size>
				 <Seed>798</Seed>
				 <Average_No_of_Reqs_in_Queue>8</Average_No_of_Reqs_in_Queue>
				 <Stop_Time>100000000000</Stop_Time>
				 <Total_Requests_To_Generate>0</Total_Requests_To_Generate>
		</IO_Flow_Parameter_Set_Synthetic>
		<IO_Flow_Parameter_Set_Synthetic>
				 <Priority_Class>HIGH</Priority_Class>
				 <Device_Level_Data_Caching_Mode>WRITE_CACHE</Device_Level_Data_Caching_Mode>
				 <Channel_IDs>4,5,6,7</Channel_IDs>
				 <Chip_IDs>0,1,2,3</Chip_IDs>
				 <Die_IDs>0,1</Die_IDs>
				 <Plane_IDs>0,1</Plane_IDs>
				 <Initial_Occupancy_Percentage>89</Initial_Occupancy_Percentage>
				 <Working_Set_Percentage>100</Working_Set_Percentage>
				 <Synthetic_Generator_Type>QUEUE_DEPTH</Synthetic_Generator_Type>
				 <Read_Percentage>0</Read_Percentage>
				 <Address_Distribution>RANDOM_HOTCOLD</Address_Distribution>
				 <Percentage_of_Hot_Region>50</Percentage_of_Hot_Region>
				 <Generated_Aligned_Addresses>true</Generated_Aligned_Addresses>
				 <Address_Alignment_Unit>16</Address_Alignment_Unit>
				 <Request_Size_Distribution>FIXED</Request_Size_Distribution>
				 <Average_Request_Size>8</Average_Request_Size>
				 <Variance_Request_Size>0</Variance_Request_Size>
				 <Seed>798</Seed>
				 <Average_No_of_Reqs_in_Queue>256</Average_No_of_Reqs_in_Queue>
				 <Stop_Time>100000000000</Stop_Time>
				 <Total_Requests_To_Generate>0</Total_Requests_To_Generate>
		</IO_Flow_Parameter_Set_Synthetic>
	</IO_Scenario>
</MQSim_IO_Scenarios>