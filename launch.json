// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug MQSim (WSL)",
      "type": "cppdbg",
      "request": "launch",
      "program": "${workspaceFolder}/MQSim",
      "args": ["-i", "ssdconfig.xml", "-w", "workload.xml"],
      "stopAtEntry": false,
      "cwd": "${workspaceFolder}",
      "externalConsole": false,
      "MIMode": "gdb",
      "miDebuggerPath": "/usr/bin/gdb",     // 指向 WSL 里的 gdb
      "setupCommands": [
        {
          "description": "Enable pretty-printing for gdb",
          "text": "-enable-pretty-printing",
          "ignoreFailures": true
        }
      ]
    }
  ]
}
