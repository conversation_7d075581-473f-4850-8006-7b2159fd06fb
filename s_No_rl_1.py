import numpy as np

base_pec = 3000  # 基础PEC值，模拟初始不均匀磨损


def calculate_rber(idle_time, page_type, pec):
    """计算RBER (原始误码率)
    使用新的误码率模型:
    当 EPC<=1000 时:
        Ylow = (0.0002 + 0.0005⋅EPC - 0.0001⋅PageType0 - 0.0001⋅PageType1)⋅T +
               (-0.0019 + 0.0065⋅EPC + 0.0031⋅PageType0 + 0.0036⋅PageType1)

    当 EPC>=1500 时:
        Yhigh = (0.0510 - 0.0033⋅EPC + 0.0012⋅PageType0 + 0.0016⋅PageType1)⋅
                exp((-0.0161 + 0.0466⋅EPC - 0.0002⋅PageType0 + 0.0015⋅PageType1)⋅T) - 0.0467

    当 1000<EPC<1500 时:
        Y = (1−w(EPC))⋅Ylow + w(EPC)⋅Yhigh
        其中，w(EPC) = 1/(1+exp(−0.00594⋅(EPC−1500)))

    PageType0和PageType1是dummy变量:
    PageType0=1，PageType1=0代表LP (原LSB/SLC)
    PageType0=0，PageType1=1代表MP (原CSB/MLC)
    PageType0=0，PageType1=0代表UP (原MSB/TLC)
    """
    # 空闲时间转换为小时
    T = idle_time / (3.6e12)  # ns转换为小时 (1h = 3.6e12 ns)

    # 设置页面类型的dummy变量
    if page_type == "LP":  # 原LSB/SLC
        PageType0 = 1
        PageType1 = 0
    elif page_type == "MP":  # 原CSB/MLC
        PageType0 = 0
        PageType1 = 1
    else:  # UP (原MSB/TLC)
        PageType0 = 0
        PageType1 = 0

    # 对PEC进行标准化 (300-3000范围)
    MIN_PEC = 0
    MAX_PEC = 3000

    # 保留原始PEC用于条件判断
    original_pec = pec

    # 标准化PEC值用于计算
    # normalized_pec = max(MIN_PEC, min(MAX_PEC, pec))
    normalized_pec = (pec - MIN_PEC) / (MAX_PEC - MIN_PEC)

    # 根据原始PEC值选择不同的计算公式
    if original_pec <= 1000:
        # 低PEC公式 - 更新为新的Ylow表达式
        Ylow = (0.0002 + 0.0005 * normalized_pec - 0.0001 * PageType0 - 0.0001 * PageType1) * T + \
               (-0.0019 + 0.0065 * normalized_pec + 0.0031 * PageType0 + 0.0036 * PageType1)
        return Ylow

    elif original_pec >= 2000:
        # 高PEC公式 - 更新为新的Yhigh表达式
        exp_factor = (-0.0161 + 0.0466 * normalized_pec - 0.0002 * PageType0 + 0.0015 * PageType1) * T
        Yhigh = (0.0510 - 0.0033 * normalized_pec + 0.0012 * PageType0 + 0.0016 * PageType1) * \
                np.exp(exp_factor) - 0.0467
        return Yhigh

    else:
        # 中间PEC区域，使用加权平均
        # 计算权重函数 w(EPC)
        w_epc = 1 / (1 + np.exp(-0.00594 * (original_pec - 1500)))

        # 计算Ylow - 更新为新的Ylow表达式
        Ylow = (0.0002 + 0.0005 * normalized_pec - 0.0001 * PageType0 - 0.0001 * PageType1) * T + \
               (-0.0019 + 0.0065 * normalized_pec + 0.0031 * PageType0 + 0.0036 * PageType1)

        # 计算Yhigh - 更新为新的Yhigh表达式
        exp_factor = (-0.0161 + 0.0466 * normalized_pec - 0.0002 * PageType0 + 0.0015 * PageType1) * T
        Yhigh = (0.0510 - 0.0033 * normalized_pec + 0.0012 * PageType0 + 0.0016 * PageType1) * \
                np.exp(exp_factor) - 0.0467

        # 加权平均
        Y = (1 - w_epc) * Ylow + w_epc * Yhigh
        return Y


def calculate_reward(rber):
    """根据RBER计算奖励

    基于RBER计算延时，然后根据延时和RBER值分段映射到不同的奖励范围:
    1. 当RBER < 0.005时，根据delay映射到[3, 0]范围
    2. 当0.005 ≤ RBER ≤ 0.013时，根据delay在[85, 1099]范围映射到[0, -6]
    3. 当RBER > 0.013时，直接设置为-7
    """
    # 固态因子，可调整
    solid_factor = 0.6

    # 根据RBER(x)计算延时(y)
    if rber < 0.005:
        delay = 85 * solid_factor + (rber / 0.005) * 85 * (1 - solid_factor)
    elif rber < 0.006:
        delay = 85 + 109 * solid_factor + ((rber - 0.005) / 0.001) * 109 * (1 - solid_factor)
    elif rber < 0.008:
        delay = 194 + 133 * solid_factor + ((rber - 0.006) / 0.002) * 133 * (1 - solid_factor)
    elif rber < 0.009:
        delay = 327 + 157 * solid_factor + ((rber - 0.008) / 0.001) * 157 * (1 - solid_factor)
    elif rber < 0.01:
        delay = 484 + 181 * solid_factor + ((rber - 0.009) / 0.001) * 181 * (1 - solid_factor)
    elif rber < 0.012:
        delay = 665 + 205 * solid_factor + ((rber - 0.01) / 0.002) * 205 * (1 - solid_factor)
    elif rber <= 0.013:
        delay = 870 + 229 * solid_factor + ((rber - 0.012) / 0.001) * 229 * (1 - solid_factor)
    else:  # rber > 0.013
        delay = 1099 + ((rber - 0.013) / 0.001) * 229

    return delay

