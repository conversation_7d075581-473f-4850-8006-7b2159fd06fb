#ifndef PARAMETER_SET_BASE_H
#define PARAMETER_SET_BASE_H

#include "../utils/rapidxml/rapidxml.hpp"
#include "../utils/XMLWriter.h"

// Parameter class template with HasValue property
template <typename T>
class Parameter {
public:
	Parameter() : HasValue(false) {}
	Parameter(const T& value) : Value(value), HasValue(true) {}
	
	operator T() const { return Value; }
	Parameter<T>& operator=(const T& value) { Value = value; HasValue = true; return *this; }
	
	T Value;
	bool HasValue;
};

class Parameter_Set_Base
{
public:
	virtual void XML_serialize(Utils::XmlWriter& xmlwriter) = 0;
	virtual void XML_deserialize(rapidxml::xml_node<> *node) = 0;
};

#endif // !PARAMETER_SET_BASE_H