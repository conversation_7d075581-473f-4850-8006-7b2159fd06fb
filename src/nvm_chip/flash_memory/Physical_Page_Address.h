#ifndef PHYSICAL_PAGE_ADDRESS_H
#define PHY<PERSON>CAL_PAGE_ADDRESS_H

#include "../NVM_Memory_Address.h"
#include "FlashTypes.h"

namespace NVM
{
	namespace FlashMemory
	{
		class Physical_Page_Address : public NVM_Memory_Address
		{
		private:
			static bool block_address_constraint_for_multiplane;//Block address of the commands to neighbor planes must be identical for multiplane command execution
		public:
			flash_channel_ID_type ChannelID;
			flash_chip_ID_type ChipID;        //The flashchip ID inside its channel
			flash_die_ID_type DieID;
			flash_plane_ID_type PlaneID;
			flash_block_ID_type BlockID;
			flash_page_ID_type PageID;
			
			// New field: Unified block identifier for consistent identification across strategies
			uint64_t unified_block_id;

			Physical_Page_Address(const flash_channel_ID_type channel_id = 0, const flash_chip_ID_type chip_id = 0, const flash_die_ID_type die_id = 0,
				const flash_plane_ID_type plane_id = 0, const flash_block_ID_type block_id = 0, const flash_page_ID_type page_id = 0)
			{
				ChannelID = channel_id;
				ChipID = chip_id;
				DieID = die_id;
				PlaneID = plane_id;
				BlockID = block_id;
				PageID = page_id;
				
				// Initialize unified block ID
				unified_block_id = generate_unified_block_id();
			}

			Physical_Page_Address(const Physical_Page_Address& addressToCopy)
			{
				ChannelID = addressToCopy.ChannelID;
				ChipID = addressToCopy.ChipID;
				DieID = addressToCopy.DieID;
				PlaneID = addressToCopy.PlaneID;
				BlockID = addressToCopy.BlockID;
				PageID = addressToCopy.PageID;
				unified_block_id = addressToCopy.unified_block_id;
			}

			static void SetBlockAddressConstraint(const bool BAConstraint)
			{
				block_address_constraint_for_multiplane = BAConstraint;
			}
			
			// Helper method to generate a unique ID for the block
			inline uint64_t generate_unified_block_id()
			{
				// Create a unified block ID that combines channel, chip, die, plane, and block
				uint64_t id = 0;
				id |= static_cast<uint64_t>(ChannelID) << 48;
				id |= static_cast<uint64_t>(ChipID) << 40;
				id |= static_cast<uint64_t>(DieID) << 32;
				id |= static_cast<uint64_t>(PlaneID) << 24;
				id |= static_cast<uint64_t>(BlockID);
				return id;
			}
			
			// Helper method to update unified block ID after changes to fields
			inline void update_unified_block_id()
			{
				unified_block_id = generate_unified_block_id();
			}
			
			// Compare two addresses at block level (ignoring page)
			inline bool is_same_block(const Physical_Page_Address& other) const
			{
				return (ChannelID == other.ChannelID && 
						ChipID == other.ChipID && 
						DieID == other.DieID && 
						PlaneID == other.PlaneID && 
						BlockID == other.BlockID);
			}
		};
	}
}
#endif // !PageAddress_H
