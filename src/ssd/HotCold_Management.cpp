#include "HotCold_Management.h"
#include "Address_Mapping_Unit_Page_Level.h"
#include "../utils/Helper_Functions.h"
#include <fstream>
#include <iostream>
#include <algorithm>
#include <iomanip> // Required for std::setw

namespace SSD_Components
{
    HotCold_Management::HotCold_Management(Address_Mapping_Unit_Page_Level* address_mapping_unit, Flash_Block_Manager_Base* block_manager)
        : address_mapping_unit(address_mapping_unit), block_manager(block_manager)
    {
        // Initialize hardcoded values
        enable_hotcold_data_separation = false;
        use_dynamic_thresholds = false;
        base_PEC = 3000;  // Will be updated from config

        // 写死的参数值
        disable_refresh = true;
        hot_blocks_count = 8;
        warm_blocks_count = 8;
        cold_blocks_count = 8;
        access_count_threshold_hot = 4;
        access_count_threshold_warm = 2;
        access_time_threshold_hot = 1 * 3600;  // 1 hour in seconds
        access_time_threshold_warm = 6 * 3600; // 6 hours in seconds
        refresh_interval_hours = 24;
        use_refresh_rewrite = true;
        refresh_percentage = 10;
        io_request_count_for_dynamic_threshold_update = 10000;
        sliding_window_size = 10000;
        io_counter = 0;
        last_refresh_time = 0;

        // Get flash properties from address_mapping_unit later in initialize()
    }

    HotCold_Management::~HotCold_Management()
    {
        // Clean up resources
    }

    void HotCold_Management::initialize()
    {
        // Get flash properties from address mapping unit
        channel_count = address_mapping_unit->channel_count;
        chip_no_per_channel = address_mapping_unit->chip_no_per_channel;
        die_no_per_chip = address_mapping_unit->die_no_per_chip;
        plane_no_per_die = address_mapping_unit->plane_no_per_die;
        block_no_per_plane = address_mapping_unit->block_no_per_plane;
        pages_no_per_block = address_mapping_unit->pages_no_per_block;

        // 从ssdconfig.xml配置文件读取冷热策略模块的关键参数

        // 读取冷热数据分离功能开关，赋值给enable_hotcold_data_separation变量
        // true表示启用冷热数据分离策略，false表示禁用
        enable_hotcold_data_separation = Device_Parameter_Set::HotCold_Separation_Enable;

        // 读取动态阈值开关，赋值给use_dynamic_thresholds变量
        // true表示使用动态阈值进行热度分类，false表示使用固定阈值
        use_dynamic_thresholds = Device_Parameter_Set::HotCold_Use_Dynamic_Thresholds;

        // 读取统一的基础PEC值，赋值给base_PEC变量（用于热度计算和块分类）
        base_PEC = Device_Parameter_Set::Base_PEC;

        // Calculate sliding window size for dynamic mode
        if (use_dynamic_thresholds) {
            sliding_window_size = 10000 + (90000 * (3000 - std::min(3000u, base_PEC)) / 3000);
        }

        // Simplified startup output
        std::cout << std::endl;
        std::cout << "=== Hot-Cold Strategy Status ===" << std::endl;
        if (enable_hotcold_data_separation) {
            std::cout << "• Hot-Cold Management: ENABLED" << std::endl;
            std::cout << "• Dynamic Thresholds: " << (use_dynamic_thresholds ? "ENABLED" : "DISABLED") << std::endl;
            std::cout << "• Base PEC: " << base_PEC << std::endl;
        } else {
            std::cout << "• Hot-Cold Management: DISABLED" << std::endl;
        }
    }

    void HotCold_Management::update_lpa_access_info(const LPA_type lpa, const stream_id_type stream_id)
    {
        // Only update if hot-cold management is enabled
        if (!enable_hotcold_data_separation) {
            return;
        }

        // Update IO counter
        io_counter++;
        
        // 更新动态阈值 - 确保即使设置了use_dynamic_thresholds也能被真正触发
        // 每当IO计数器达到阈值时更新
        if (use_dynamic_thresholds && (io_counter % io_request_count_for_dynamic_threshold_update == 0)) {
            update_dynamic_thresholds();
        }

        // Get current time
        sim_time_type current_time = Simulator->Time();

        // Create or update LPA entry
        auto it = lpa_access_info.find(lpa);
        if (it == lpa_access_info.end()) {
            // First time access, create a new entry
            LPA_Entry entry;
            entry.access_count = 1;
            entry.last_access_time = current_time;
            entry.temperature = DataTemperature::COLD; // Default to COLD for new entries
            lpa_access_info[lpa] = entry;
        } else {
            // Update existing entry
            it->second.access_count++;
            it->second.last_access_time = current_time;

            // Update temperature based on current thresholds
            if (it->second.access_count >= access_count_threshold_hot) {
                it->second.temperature = DataTemperature::HOT;
            } else if (it->second.access_count >= access_count_threshold_warm) {
                it->second.temperature = DataTemperature::WARM;
            }
        }

        // Check if refresh is needed
        if (!disable_refresh) {
            sim_time_type elapsed_time = current_time - last_refresh_time;
            if (elapsed_time >= refresh_interval_hours * 3600 * 1000000000ULL) {
        check_and_perform_refresh();
                last_refresh_time = current_time;
            }
        }
    }

    DataTemperature HotCold_Management::get_data_temperature(const LPA_type lpa)
    {
        if (!enable_hotcold_data_separation) return DataTemperature::COLD;

        auto it = lpa_access_info.find(lpa);
        if (it == lpa_access_info.end()) {
            return DataTemperature::COLD;
        }

        // Calculate the time difference in simulator time units and convert to seconds
        sim_time_type current_time = Simulator->Time();
        sim_time_type time_diff = current_time - it->second.last_access_time;
        double time_diff_sec = static_cast<double>(time_diff) / SIM_TIME_TO_SECONDS_COEFF;

        // Check access count and time thresholds
        if (it->second.access_count >= access_count_threshold_hot && time_diff_sec <= access_time_threshold_hot) {
            it->second.temperature = DataTemperature::HOT;
        } else if (it->second.access_count >= access_count_threshold_warm && time_diff_sec <= access_time_threshold_warm) {
            it->second.temperature = DataTemperature::WARM;
        } else {
            it->second.temperature = DataTemperature::COLD;
        }

        return it->second.temperature;
    }

    void HotCold_Management::allocate_plane_based_on_temperature(NVM_Transaction_Flash_WR* transaction)
    {
        // First, update LPA access information
        update_lpa_access_info(transaction->LPA, transaction->Stream_id);

        // Use the original plane allocation scheme (preserving channel, chip, die, plane calculation)
        address_mapping_unit->allocate_plane_for_user_write(transaction);
    }

    int HotCold_Management::calculate_plane_key(const NVM::FlashMemory::Physical_Page_Address& address)
    {
        // Create a unique key for each plane
        return (address.ChannelID * 1000000) + (address.ChipID * 10000) + (address.DieID * 100) + address.PlaneID;
    }

    BlockPoolEntry HotCold_Management::get_block_for_temperature(DataTemperature temperature, int plane_key, const stream_id_type stream_id)
    {
        std::vector<BlockPoolEntry>* target_pool = nullptr;

        // Select the appropriate pool based on data temperature
        switch (temperature) {
            case DataTemperature::HOT:
                target_pool = &hot_block_pool[plane_key];
                break;
            case DataTemperature::WARM:
                target_pool = &warm_block_pool[plane_key];
                break;
            case DataTemperature::COLD:
                target_pool = &cold_block_pool[plane_key];
                break;
        }

        // Initialize the pool if it doesn't exist
        if (target_pool->empty()) {
            NVM::FlashMemory::Physical_Page_Address address;
            address.ChannelID = (plane_key / 1000000) % 100;
            address.ChipID = (plane_key / 10000) % 100;
            address.DieID = (plane_key / 100) % 100;
            address.PlaneID = plane_key % 100;

            // Get a free block from the block manager
            BlockPoolEntry entry;
            PlaneBookKeepingType* plane_record = block_manager->Get_plane_bookkeeping_entry(address);
            Block_Pool_Slot_Type* block = plane_record->Get_a_free_block(stream_id, false);

            entry.block_id = block->BlockID;
            entry.next_page_id = 0; // Start from first page
            target_pool->push_back(entry);
            
            // Add this block to our statistics tracking with the current temperature
            unsigned long block_key = calculate_block_id(address.ChannelID, address.ChipID, address.DieID, address.PlaneID, entry.block_id);
            if (block_statistics.find(block_key) == block_statistics.end()) {
                BlockStatistics stats;
                stats.block_id = entry.block_id;
                stats.channel_id = address.ChannelID;
                stats.chip_id = address.ChipID;
                stats.die_id = address.DieID;
                stats.plane_id = address.PlaneID;
                stats.historical_access_count = 0;
                stats.recent_access_count = 0;
                stats.score = 0.0f;
                stats.current_temperature = temperature;
                block_statistics[block_key] = stats;
            } else {
                block_statistics[block_key].current_temperature = temperature;
            }
        }

        // Get the current entry
        BlockPoolEntry& current_entry = (*target_pool)[0];

        // If current block is full, get a new one
        if (current_entry.next_page_id >= pages_no_per_block) {
            NVM::FlashMemory::Physical_Page_Address address;
            address.ChannelID = (plane_key / 1000000) % 100;
            address.ChipID = (plane_key / 10000) % 100;
            address.DieID = (plane_key / 100) % 100;
            address.PlaneID = plane_key % 100;

            // Get a free block from the block manager
            PlaneBookKeepingType* plane_record = block_manager->Get_plane_bookkeeping_entry(address);
            Block_Pool_Slot_Type* block = plane_record->Get_a_free_block(stream_id, false);

            current_entry.block_id = block->BlockID;
            current_entry.next_page_id = 0; // Start from first page
            
            // Add this block to our statistics tracking with the current temperature
            unsigned long block_key = calculate_block_id(address.ChannelID, address.ChipID, address.DieID, address.PlaneID, current_entry.block_id);
            if (block_statistics.find(block_key) == block_statistics.end()) {
                BlockStatistics stats;
                stats.block_id = current_entry.block_id;
                stats.channel_id = address.ChannelID;
                stats.chip_id = address.ChipID;
                stats.die_id = address.DieID;
                stats.plane_id = address.PlaneID;
                stats.historical_access_count = 0;
                stats.recent_access_count = 0;
                stats.score = 0.0f;
                stats.current_temperature = temperature;
                block_statistics[block_key] = stats;
            } else {
                block_statistics[block_key].current_temperature = temperature;
            }
        }

        return current_entry;
    }

    void HotCold_Management::allocate_page_based_on_temperature(NVM_Transaction_Flash_WR* transaction)
    {
        if (!enable_hotcold_data_separation) {
            address_mapping_unit->allocate_page_in_plane_for_user_write(transaction, false);
            return;
        }

        // Determine data temperature based on LPA access information
        DataTemperature temp = get_data_temperature(transaction->LPA);

        // Calculate the plane key
        int plane_key = calculate_plane_key(transaction->Address);

        // Get block and page from the appropriate temperature pool
        BlockPoolEntry entry = get_block_for_temperature(temp, plane_key, transaction->Stream_id);

        // Get old PPA if exists (for invalidation)
        AddressMappingDomain* domain = address_mapping_unit->domains[transaction->Stream_id];
        PPA_type old_ppa = domain->Get_ppa(address_mapping_unit->ideal_mapping_table, transaction->Stream_id, transaction->LPA);
        
        if (old_ppa != NO_PPA) {
            page_status_type prev_page_status = domain->Get_page_status(address_mapping_unit->ideal_mapping_table, transaction->Stream_id, transaction->LPA);
            page_status_type status_intersection = transaction->write_sectors_bitmap & prev_page_status;
            
            // Check if an update read is required
            if (status_intersection == prev_page_status) {
                NVM::FlashMemory::Physical_Page_Address addr;
                address_mapping_unit->Convert_ppa_to_address(old_ppa, addr);
                block_manager->Invalidate_page_in_block(transaction->Stream_id, addr);
            } else {
                page_status_type read_pages_bitmap = status_intersection ^ prev_page_status;
                NVM_Transaction_Flash_RD* update_read_tr = new NVM_Transaction_Flash_RD(transaction->Source, transaction->Stream_id,
                    count_sector_no_from_status_bitmap(read_pages_bitmap) * SECTOR_SIZE_IN_BYTE, transaction->LPA, old_ppa, transaction->UserIORequest,
                    transaction->Content, transaction, read_pages_bitmap, domain->GlobalMappingTable[transaction->LPA].TimeStamp);
                address_mapping_unit->Convert_ppa_to_address(old_ppa, update_read_tr->Address);
                block_manager->Read_transaction_issued(update_read_tr->Address);
                block_manager->Invalidate_page_in_block(transaction->Stream_id, update_read_tr->Address);
                transaction->RelatedRead = update_read_tr;
            }
        }

        // Assign block and page to the transaction
        transaction->Address.BlockID = entry.block_id;
        transaction->Address.PageID = entry.next_page_id++;
        //std::cout << "clb_HotCold_Management " << transaction->Address.ChannelID << " " << transaction->Address.ChipID << " " << transaction->Address.DieID << " " << transaction->Address.PlaneID << " " << transaction->Address.BlockID << " " << transaction->Address.PageID << std::endl;
        transaction->PPA = address_mapping_unit->Convert_address_to_ppa(transaction->Address);
        
        // Update block access statistics
        update_block_access(transaction->Address.ChannelID, transaction->Address.ChipID,
                         transaction->Address.DieID, transaction->Address.PlaneID,
                         transaction->Address.BlockID);
        
        // Update the entry in the pool
        switch (temp) {
            case DataTemperature::HOT:
                hot_block_pool[plane_key][0] = entry;
                break;
            case DataTemperature::WARM:
                warm_block_pool[plane_key][0] = entry;
                break;
            case DataTemperature::COLD:
                cold_block_pool[plane_key][0] = entry;
                break;
        }

        // Update mapping table - use Read_transaction_issued before program since we don't have direct access to program_transaction_issued
        block_manager->Read_transaction_issued(transaction->Address);  // Mark the block as being used
        domain->Update_mapping_info(address_mapping_unit->ideal_mapping_table, transaction->Stream_id, transaction->LPA, transaction->PPA,
            transaction->write_sectors_bitmap | domain->Get_page_status(address_mapping_unit->ideal_mapping_table, transaction->Stream_id, transaction->LPA));
        transaction->Physical_address_determined = true;
    }

    void HotCold_Management::update_dynamic_thresholds()
    {
        if (!use_dynamic_thresholds) {
            return; // 如果没有启用动态阈值策略，则直接返回
        }

        // 存储旧的阈值用于比较
        unsigned int old_access_count_threshold_hot = access_count_threshold_hot;
        unsigned int old_access_count_threshold_warm = access_count_threshold_warm;
        unsigned int old_access_time_threshold_hot = access_time_threshold_hot;
        unsigned int old_access_time_threshold_warm = access_time_threshold_warm;

        // 动态阈值策略实现
        // 根据IO模式和工作负载特性调整访问计数和访问时间阈值
        
        // 1. 计算有效PEC (从配置中读取base_PEC，不使用硬编码值)
        unsigned int effective_pec = base_PEC;
        // 使用3000作为最大PEC值进行归一化
        double pec_ratio = std::min(1.0, static_cast<double>(effective_pec) / 3000.0);
        double pec_factor = 1.0 - pec_ratio; // PEC越大，因子越小（负相关）
        
        // 2. 更新工作负载强度 - 使用固定周期50000个IO和移动平均
        sim_time_type current_time = Simulator->Time();
        const unsigned int UPDATE_PERIOD = 50000; // 固定更新周期为50000个IO
        
        // 如果是第一次更新（上次更新时间为0），则初始化
        if (last_intensity_update_time == 0) {
            // 初始化时间戳
            last_intensity_update_time = current_time;
            // 第一次调用时，还没有足够数据计算，设置为当前强度
            double cur_intensity = (static_cast<double>(UPDATE_PERIOD) / current_time) * 1e9;
                
            // 归一化，假设10000 IO/s是高强度(1.0)
            workload_intensity = std::min(1.0, cur_intensity / 10000.0);
        } else {
            // 计算当前时间窗口内的工作负载强度
            sim_time_type time_diff = current_time - last_intensity_update_time;
            if (time_diff > 0) {
                // 计算当前负载强度：周期内平均每纳秒的IO数量 * 纳秒/秒 = IO每秒
                double current_intensity = (static_cast<double>(UPDATE_PERIOD) / time_diff) * 1e9;
                
                // 归一化，假设10000 IO/s是高强度(1.0)
                current_intensity = std::min(1.0, current_intensity / 10000.0);
                
                // 使用移动平均计算最终工作负载强度
                // alpha参数决定新样本的权重：0.3表示新样本占30%，旧值占70%
                const double alpha = 0.3;
                workload_intensity = alpha * current_intensity + (1.0 - alpha) * workload_intensity;
            }
        }
        
        // 更新最后更新时间
        last_intensity_update_time = current_time;
        
        // 3. 结合PEC和工作负载强度，各占0.5权重
        double combined_weight = pec_factor * 0.5 + workload_intensity * 0.5;
        
        // 4. 访问计数阈值调整 - 结合PEC和工作负载强度
        // 随PEC增加而减小，随工作负载强度增加而增大
        access_count_threshold_hot = std::max(2u, 2+static_cast<unsigned int>(4.0 * combined_weight));
        //access_count_threshold_warm = std::max(1u, static_cast<unsigned int>(4.0 * combined_weight));
        access_count_threshold_warm = access_count_threshold_hot/2;

        // 5. 访问时间阈值调整 - 结合PEC和工作负载强度
        // 随PEC增加而减小，随工作负载强度增加而增大
        access_time_threshold_hot = 10 - static_cast<int>(8.0 * combined_weight);
        //access_time_threshold_warm = std::max(2u, static_cast<unsigned int>(5.0 * combined_weight));
        access_time_threshold_warm = access_time_threshold_hot/2;

        // 如果阈值有变化，输出调试信息
        if (old_access_count_threshold_hot != access_count_threshold_hot ||
            old_access_count_threshold_warm != access_count_threshold_warm ||
            old_access_time_threshold_hot != access_time_threshold_hot ||
            old_access_time_threshold_warm != access_time_threshold_warm) {
            
            std::cout << "[HotCold动态阈值更新] IO计数:" << io_counter
                    << " PEC:" << effective_pec
                    << " Max_PEC:3000"
                    << " 工作负载强度:" << workload_intensity
                    << " 组合权重:" << combined_weight
                    << " 热计数阈值:" << access_count_threshold_hot << "(旧:" << old_access_count_threshold_hot << ")"
                    << " 暖计数阈值:" << access_count_threshold_warm << "(旧:" << old_access_count_threshold_warm << ")"
                    << " 热时间阈值:" << access_time_threshold_hot << "(旧:" << old_access_time_threshold_hot << ")"
                    << " 暖时间阈值:" << access_time_threshold_warm << "(旧:" << old_access_time_threshold_warm << ")" << std::endl;
        }
    }

    unsigned long HotCold_Management::calculate_block_id(flash_channel_ID_type channel_id, flash_chip_ID_type chip_id,
                                                     flash_die_ID_type die_id, flash_plane_ID_type plane_id,
                                                     flash_block_ID_type block_id)
    {
        return ((unsigned long)channel_id << 40) | ((unsigned long)chip_id << 32) | 
               ((unsigned long)die_id << 24) | ((unsigned long)plane_id << 16) | block_id;
    }

    void HotCold_Management::update_block_access(flash_channel_ID_type channel_id, flash_chip_ID_type chip_id,
                                            flash_die_ID_type die_id, flash_plane_ID_type plane_id,
                                            flash_block_ID_type block_id)
    {
        unsigned long key = calculate_block_id(channel_id, chip_id, die_id, plane_id, block_id);
        auto it = block_statistics.find(key);
        if (it != block_statistics.end()) {
            it->second.recent_access_count++;
            it->second.historical_access_count++;
        }
    }

    void HotCold_Management::recalculate_block_scores()
    {
        for (auto& pair : block_statistics) {
            // Simple score calculation: recent access count with decay
            pair.second.score = 0.7f * pair.second.recent_access_count + 0.3f * pair.second.score;
            pair.second.recent_access_count = 0;
        }
    }

    void HotCold_Management::reclassify_blocks()
    {
        // This would be a complex function to reclassify blocks based on their scores
        // For simplicity, we won't implement the full logic here
    }

    void HotCold_Management::check_and_perform_refresh()
    {
        if (disable_refresh) return;

        // Check if it's time to refresh
        sim_time_type current_time = Simulator->Time();
        sim_time_type refresh_interval_ns = refresh_interval_hours * 3600ULL * SIM_TIME_TO_SECONDS_COEFF;

        if (current_time - last_refresh_time > refresh_interval_ns) {
            // Perform refresh operation
            // This would be a complex operation, not fully implemented here
            last_refresh_time = current_time;
        }
    }

    void HotCold_Management::handle_read_transaction(NVM_Transaction_Flash_RD* transaction)
    {
        if (!enable_hotcold_data_separation) return;

        // Update LPA access information for the read operation
        update_lpa_access_info(transaction->LPA, transaction->Stream_id);

        // Optionally update the temperature classification right away
        //get_data_temperature(transaction->LPA);
    }
} 