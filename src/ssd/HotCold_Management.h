#ifndef HOTCOLD_MANAGEMENT_H
#define HOTCOLD_MANAGEMENT_H

#include <unordered_map>
#include <vector>
#include <queue>
#include <map>
#include <memory>
#include <fstream>
#include "NVM_Transaction_Flash_RD.h"
#include "NVM_Transaction_Flash_WR.h"
#include "Flash_Block_Manager_Base.h"
#include "../sim/Sim_Defs.h"

namespace SSD_Components
{
    // Forward declarations
    class Address_Mapping_Unit_Page_Level;

    // Data temperature classification
    enum class DataTemperature {COLD, WARM, HOT};

    // Block pool entry structure
    struct BlockPoolEntry {
        flash_block_ID_type block_id;
        flash_page_ID_type next_page_id;
    };

    // Block statistics for tracking
    struct BlockStatistics {
        flash_block_ID_type block_id;
        flash_channel_ID_type channel_id;
        flash_chip_ID_type chip_id;
        flash_die_ID_type die_id;
        flash_plane_ID_type plane_id;
        unsigned long historical_access_count;
        unsigned int recent_access_count;
        float score;
        DataTemperature current_temperature;
    };

    // LPA access information
    struct LPA_Entry {
        unsigned int access_count;
        sim_time_type last_access_time;
        DataTemperature temperature;
    };

    /*
     * HotCold_Management class is responsible for tracking data access patterns and
     * classifying data into temperature categories (hot, warm, cold) based on their access frequency and recency.
     * Both read and write operations are considered for data temperature classification.
     * Hot data is placed in dedicated blocks to improve performance and reduce write amplification.
     */
    class HotCold_Management
    {
    public:
        HotCold_Management(Address_Mapping_Unit_Page_Level* address_mapping_unit, Flash_Block_Manager_Base* block_manager);
        ~HotCold_Management();

        // Main functions
        void initialize();
        void update_lpa_access_info(const LPA_type lpa, const stream_id_type stream_id);
        DataTemperature get_data_temperature(const LPA_type lpa);
        void allocate_plane_based_on_temperature(NVM_Transaction_Flash_WR* transaction);
        void allocate_page_based_on_temperature(NVM_Transaction_Flash_WR* transaction);
        
        // Method to handle read operations
        void handle_read_transaction(NVM_Transaction_Flash_RD* transaction);
        
        // Helper functions
        int calculate_plane_key(const NVM::FlashMemory::Physical_Page_Address& address);
        BlockPoolEntry get_block_for_temperature(DataTemperature temperature, int plane_key, const stream_id_type stream_id);
        void update_block_access(flash_channel_ID_type channel_id, flash_chip_ID_type chip_id, 
                                 flash_die_ID_type die_id, flash_plane_ID_type plane_id, 
                                 flash_block_ID_type block_id);
        unsigned long calculate_block_id(flash_channel_ID_type channel_id, flash_chip_ID_type chip_id,
                                        flash_die_ID_type die_id, flash_plane_ID_type plane_id,
                                        flash_block_ID_type block_id);
        void recalculate_block_scores();
        void reclassify_blocks();
        void update_dynamic_thresholds();
        void check_and_perform_refresh();

        // Configuration getters and setters
        void set_enable_hotcold_data_separation(bool value) { enable_hotcold_data_separation = value; }
        bool get_enable_hotcold_data_separation() { return enable_hotcold_data_separation; }
        
    private:
        // Reference to other modules
        Address_Mapping_Unit_Page_Level* address_mapping_unit;
        Flash_Block_Manager_Base* block_manager;

        // Configuration parameters
        bool enable_hotcold_data_separation;
        bool use_dynamic_thresholds;
        bool disable_refresh;  // Flag to disable refresh operations
        unsigned int hot_blocks_count;
        unsigned int warm_blocks_count;
        unsigned int cold_blocks_count;
        unsigned int access_count_threshold_hot;
        unsigned int access_count_threshold_warm;
        unsigned int access_time_threshold_hot;
        unsigned int access_time_threshold_warm;
        unsigned int refresh_interval_hours;
        bool use_refresh_rewrite;
        unsigned int refresh_percentage;
        unsigned int base_PEC;
        unsigned int io_request_count_for_dynamic_threshold_update;
        unsigned int sliding_window_size;

        // Data structure for access tracking
        std::unordered_map<LPA_type, LPA_Entry> lpa_access_info;

        // 工作负载监测变量
        double workload_intensity = 0.0;     // 当前工作负载强度，0-1
        sim_time_type last_intensity_update_time = 0;  // 最后更新工作负载强度的时间

        // Block pools for hot, warm, cold data
        std::unordered_map<int, std::vector<BlockPoolEntry>> hot_block_pool; // key: channel*chip*die*plane
        std::unordered_map<int, std::vector<BlockPoolEntry>> warm_block_pool;
        std::unordered_map<int, std::vector<BlockPoolEntry>> cold_block_pool;

        // Block statistics tracking
        std::map<unsigned long, BlockStatistics> block_statistics;

        // Counter for IO requests to trigger dynamic threshold updates
        unsigned int io_counter;
        sim_time_type last_refresh_time;

        // Flash properties 
        unsigned int channel_count;
        unsigned int chip_no_per_channel;
        unsigned int die_no_per_chip;
        unsigned int plane_no_per_die;
        unsigned int block_no_per_plane;
        unsigned int pages_no_per_block;
    };
}

#endif // HOTCOLD_MANAGEMENT_H 