#include "RL_Management.h"
#include "TRE_Module.h"
#include <algorithm>
#include <numeric>
#include <random>
#include <cmath>
#include "../sim/Engine.h"
#include <queue>
#include <functional>
#include <iostream>
#include <iomanip>
#include <fstream>
#include <chrono>
#include <sstream>

namespace SSD_Components {

// Initialize static reference to the TRE module
TRE_Module* RLManager::tre_module = nullptr;

// Initialize static instance of RLManager
RLManager* RLManager::instance = nullptr;

// RLBlock 
RLBlock::RLBlock(flash_block_ID_type id) : 
    block_id(id),
    free_pages(0),
    pec(0),
    last_write_time(0),
    last_access_time(0),
    block_class(BlockClass::STATELESS),
    idle_time(0),
    base_pec(3000),  // Initial PEC value - will be updated from config
    next_write_page_index(0),
    total_access_count(0),
    channel_id(0),   // Default channel ID
    chip_id(0),      // Default chip ID
    die_id(0),       // Default die ID
    plane_id(0),     // Default plane ID
    stream_id(0),    // Default stream ID
    access_count_in_window(0) {   // Initialize access count in window

}

bool RLBlock::write_page(LPA_type page_id, sim_time_type timestamp, bool update_access_time) {
    if (free_pages <= 0 || next_write_page_index >= pages.size()) {
        return false;
    }
    
    // Use sequential write approach
    pages[next_write_page_index] = page_id;
    next_write_page_index++;
    free_pages--;
    last_write_time = timestamp;
    
    if (update_access_time) {
        last_access_time = timestamp;
        pages_last_access_time[page_id] = timestamp;
    }
    
    page_access_count[page_id] = 0;
    return true;
}

bool RLBlock::read_page(LPA_type page_id, sim_time_type timestamp, bool update_access_time) {
    auto it = std::find(pages.begin(), pages.end(), page_id);
    if (it != pages.end()) {
        if (update_access_time) {
            last_access_time = timestamp;
            pages_last_access_time[page_id] = timestamp;
        }
        
        page_access_count[page_id] = page_access_count[page_id] + 1;
        return true;
    }
    return false;
}

sim_time_type RLBlock::get_idle_time(sim_time_type current_time) {
    return current_time - last_access_time;
}

PageType RLBlock::get_next_page_type() {
    PageType types[] = {PageType::LP, PageType::MP, PageType::UP};
    return types[next_write_page_index % 3];
}

void RLBlock::erase_block(sim_time_type timestamp) {
    std::fill(pages.begin(), pages.end(), NO_LPA);
    free_pages = pages.size();
    pec++;
    last_write_time = timestamp;
    last_access_time = timestamp;
    idle_time = 0;
    page_access_count.clear();
    pages_last_access_time.clear();
    next_write_page_index = 0;
}

// RLPageInfo implementation
RLPageInfo::RLPageInfo() :
    last_block_class(BlockClass::COLD),
    last_write_time(0),
    last_block_id(NO_BLOCK),
    write_count(0),
    // Two-layer RL initialization
    last_state_layer1(-1),
    last_action_layer1(-1),
    next_state_layer1(-1),
    last_state_layer2(-1),
    last_action_layer2(-1),
    next_state_layer2(-1),
    // Legacy fields
    last_state(0),
    last_action(0),
    next_state(0),
    last_block_access_time(0),
    num_pages(1),
    page_type(PageType::LP),
    last_state_index(-1) {
}

// RLAgent implementation
RLAgent::RLAgent(int num_states, int num_actions, bool use_replay) :
    epsilon(0.8),                   // Initial exploration rate
    alpha(0.3),                     // Learning rate
    gamma(0.8),                     // Discount factor
    min_replay_size(300),           // Min replay buffer size before sampling
    replay_batch_size(16),          // Batch size for replay
    min_epsilon(0.01),              // Minimum exploration rate
    epsilon_decay(0.995),           // Epsilon decay rate
    step_count(0),
    read_count(0),
    use_experience_replay(use_replay),
    epoch_size(2000),               // Default epoch size: 2000 read operations
    current_epoch_reads(0),         // Initialize current epoch read count
    use_delayed_update(true) {      // Enable delayed Q-table updates by default

    // Initialize Q-table with small negative values instead of zeros
    q_table.resize(num_states, std::vector<double>(num_actions, -10));

    // Initialize Q-table copy for updates (same initial values)
    q_table_update.resize(num_states, std::vector<double>(num_actions, -10));

    // Set max replay buffer size with a more reasonable limit
    if (use_experience_replay) {
        // Use a more reasonable buffer size to balance memory usage and learning efficiency
        replay_buffer.reserve(5000);
    }

    // Seed all random number generators with a fixed seed for reproducibility
    srand(123456);  // Seed the global random number generator
}
/*
int RLAgent::select_action(int state) {
    step_count++;

    // Determine exploration rate based on read operations
    double current_epsilon = (read_count <= 2000) ? 0.8 : 0.01;

    // Epsilon-greedy action selection
    static std::mt19937 gen(123456);  // Fixed seed for reproducibility
    static std::uniform_real_distribution<> dist(0.0, 1.0);

    if (dist(gen) < current_epsilon) {
        // Explore: choose random action
        static std::uniform_int_distribution<> action_dist(0, q_table[0].size() - 1);
        return action_dist(gen);
    } else {
        // Exploit: choose best action (highest Q-value)
        // Use a faster method to find the max element
        const auto& q_values = q_table[state];
        int best_action = 0;
        double max_q_value = q_values[0];

        // Manual loop is faster than std::max_element for small arrays
        for (int i = 1; i < q_values.size(); i++) {
            if (q_values[i] > max_q_value) {
                max_q_value = q_values[i];
                best_action = i;
            }
        }

        return best_action;
    }
}
*/
int RLAgent::select_action_with_mask(int state, const std::vector<bool>& action_mask, bool is_after_joint) {
    step_count++;

    // === RL动作选择完整执行逻辑 ===
    // 1. 先mask不可用的动作
    // 2. 前2000个读请求80%概率随机选择动作，20%概率贪婪策略
    // 3. 之后的1%概率随机选择，99%概率贪婪策略

    // 先mask不可用的动作
    std::vector<int> valid_actions;
    for (int i = 0; i < action_mask.size(); i++) {
        if (action_mask[i]) {
            valid_actions.push_back(i);
        }
    }
    /*
    // 如果没有有效动作，回退到原始方法
    if (valid_actions.empty()) {
        return select_action(state);
    }*/

    // 根据概率决定是随机还是贪婪决策
    if (should_explore() && !is_after_joint) {
        // 随机选择动作
        return select_random_action_with_mask(state, action_mask);
    } else {
        // 贪婪选择动作
        return select_greedy_action_with_mask(state, action_mask);
    }
}

// 随机选择动作（探索）
int RLAgent::select_random_action_with_mask(int state, const std::vector<bool>& action_mask) {
    // 获取有效动作
    std::vector<int> valid_actions;
    for (int i = 0; i < action_mask.size(); i++) {
        if (action_mask[i]) {
            valid_actions.push_back(i);
        }
    }
    /*
    // 如果没有有效动作，回退到原始方法
    if (valid_actions.empty()) {
        return select_action(state);
    }*/

    // 随机选择一个有效动作
    static std::mt19937 gen(123456);  // 固定种子以保证可重现性
    std::uniform_int_distribution<> action_dist(0, valid_actions.size() - 1);
    return valid_actions[action_dist(gen)];
}

// 贪婪选择动作（利用）
int RLAgent::select_greedy_action_with_mask(int state, const std::vector<bool>& action_mask) {
    // 获取有效动作
    std::vector<int> valid_actions;
    for (int i = 0; i < action_mask.size(); i++) {
        if (action_mask[i]) {
            valid_actions.push_back(i);
        }
    }
    /*
    // 如果没有有效动作，回退到原始方法
    if (valid_actions.empty()) {
        return select_action(state);
    }*/

    // 选择Q值最高的有效动作
    const auto& q_values = q_table[state];
    int best_action = valid_actions[0];
    double max_q_value = q_values[valid_actions[0]];

    // 在有效动作中找到最佳动作
    for (int i = 1; i < valid_actions.size(); i++) {
        int action = valid_actions[i];
        if (q_values[action] > max_q_value) {
            max_q_value = q_values[action];
            best_action = action;
        }
    }

    return best_action;
}

// 判断是否应该探索
bool RLAgent::should_explore() const {
    // 前2000个读请求80%概率随机选择动作，20%概率贪婪策略
    // 之后的1%概率随机选择，99%概率贪婪策略
    static std::mt19937 gen(123456);  // 固定种子以保证可重现性
    static std::uniform_real_distribution<> dist(0.0, 1.0);

    if (read_count < 2000) {
        return dist(gen) < 0.8;  // 80%概率探索
    } else {
        return dist(gen) < 0.01;  // 1%概率探索
    }
}

void RLAgent::update_q_table(int state, int action, double reward, int next_state) {
    if (use_delayed_update) {
        // Use delayed update: update the copy Q-table instead of decision Q-table
        update_q_table_delayed(state, action, reward, next_state);
    } else {
        // Original immediate update logic
        // Find max Q-value in next state efficiently
        const auto& next_q_values = q_table[next_state];
        double max_next_q = next_q_values.empty() ? 0.0 : next_q_values[0];

        // Manual loop is faster than std::max_element for small arrays
        for (size_t i = 1; i < next_q_values.size(); i++) {
            if (next_q_values[i] > max_next_q) {
                max_next_q = next_q_values[i];
            }
        }

        // Q-Learning update formula with direct computation
        const double target = reward + gamma * max_next_q;
        const double current = q_table[state][action];
        q_table[state][action] = current + alpha * (target - current);
    }
}

double RLAgent::store_experience(int state, int action, double reward, int next_state, LPA_type lpa) {
    // Save copy of Q-table before update for calculating change rate
    double direct_q_change = 0.0;
    double old_q_value = 0.0;
    
    if (state != -1 && action != -1) {
        old_q_value = q_table[state][action];
    }
    
    // Step 1: If replay is enabled and buffer size is sufficient, perform random sampling update
    double replay_q_change = 0.0;
    if (use_experience_replay && replay_buffer.size() >= min_replay_size) {
        replay_q_change = replay(replay_batch_size);
    }
    
    // Step 2: Update Q-table with current experience
    if (state != -1 && action != -1 && next_state != -1) {
        // 使用S-A-R-S'四元组更新Q表:
        // S: 写操作前的状态 (state)
        // A: 写操作采取的动作 (action)
        // R: 读操作得到的奖励 (reward)
        // S': 写操作后的状态 (next_state)
        update_q_table(state, action, reward, next_state);

        // Calculate change rate more efficiently
        if (use_delayed_update) {
            // For delayed update, calculate change based on update Q-table
            direct_q_change = std::abs(q_table_update[state][action] - old_q_value);
        } else {
            // For immediate update, calculate change based on decision Q-table
            direct_q_change = std::abs(q_table[state][action] - old_q_value);
        }
        // If old_q_value is small, normalize differently to avoid division by small numbers
        if (std::abs(old_q_value) > 0.001) {
            direct_q_change /= std::abs(old_q_value);
        }
        q_changes.push_back(direct_q_change);
    }
    
    // Step 3: Add current experience to replay buffer with improved management
    if (use_experience_replay && state != -1 && action != -1 && next_state != -1) {
        // Check if this is an update operation (LPA already exists in buffer)
        bool found_existing = false;
        if (lpa != NO_LPA) {
            // Search for existing experience with the same LPA
            for (auto it = replay_buffer.begin(); it != replay_buffer.end(); ++it) {
                if (std::get<4>(*it) == lpa) {
                    // Found existing experience for the same LPA, replace with new information
                    *it = std::make_tuple(state, action, reward, next_state, lpa);
                    found_existing = true;
                    break;
                }
            }
        }

        // If no existing experience found, add new experience
        if (!found_existing) {
            // Use a ring buffer approach with fixed size
            if (replay_buffer.size() >= 5000) {
                // Use FIFO strategy - remove oldest entry
                replay_buffer.erase(replay_buffer.begin());
            }

            // Add current experience to buffer with S-A-R-S'-LPA format
            replay_buffer.push_back(std::make_tuple(state, action, reward, next_state, lpa));
        }
    }
    
    // Increment read count and handle epoch management for delayed updates
    if (use_delayed_update) {
        increment_read_count(); // This will also handle epoch end and Q-table sync
    } else {
        read_count++; // Simple increment for immediate updates
    }

    // Return total Q-value change rate
    return std::max(replay_q_change, direct_q_change);
}

double RLAgent::replay(unsigned int batch_size) {
    if (!use_experience_replay || replay_buffer.size() < min_replay_size) {
        return 0.0;
    }
    
    double max_q_change = 0.0;
    
    // 使用静态随机数生成器以获得更好的性能
    static std::mt19937 gen(123456);  // 固定种子以确保可重现性
    
    // 从经验池中随机选择batch_size条经验
    std::uniform_int_distribution<> dist(0, replay_buffer.size() - 1);
    
    // 预分配采样索引向量
    std::vector<size_t> sampled_indices;
    sampled_indices.reserve(batch_size);
    
    // 随机采样batch_size条经验
    while (sampled_indices.size() < batch_size && sampled_indices.size() < replay_buffer.size()) {
        size_t idx = dist(gen);
        
        // 检查索引是否已被采样
        if (std::find(sampled_indices.begin(), sampled_indices.end(), idx) == sampled_indices.end()) {
            sampled_indices.push_back(idx);
        }
    }
            
    // 处理所有采样的经验
    for (size_t idx : sampled_indices) {
        // 获取经验
        int exp_state, exp_action, exp_next_state;
        double exp_reward;
        LPA_type exp_lpa;
        std::tie(exp_state, exp_action, exp_reward, exp_next_state, exp_lpa) = replay_buffer[idx];
        
        // 存储旧的Q值以计算变化
        double old_q_value;
        if (use_delayed_update) {
            old_q_value = q_table_update[exp_state][exp_action];
        } else {
            old_q_value = q_table[exp_state][exp_action];
        }

        // 更新Q表
        update_q_table(exp_state, exp_action, exp_reward, exp_next_state);

        // 计算Q值变化
        double q_change;
        if (use_delayed_update) {
            q_change = std::abs(q_table_update[exp_state][exp_action] - old_q_value);
        } else {
            q_change = std::abs(q_table[exp_state][exp_action] - old_q_value);
        }
        if (std::abs(old_q_value) > 0.001) {
            q_change /= std::abs(old_q_value);
        }
        
        max_q_change = std::max(max_q_change, q_change);
    }
    
    return max_q_change;
}



// RLManager implementation
RLManager::RLManager(unsigned int total_blocks, unsigned int pages_per_block, Flash_Block_Manager_Base* block_manager, bool use_exp_replay) :
    write_count(0),
    read_count(0),
    read_miss_count(0),
    write_miss_count(0),
    page_type_mismatch_count(0),
    page_type_match_count(0),
    page_type_search_attempts(0),
    tre_count(0),
    total_read_count(0),
    total_reward(0),
    avg_pec(0),
    total_io_count(0),
    total_requests(0),
    current_time(0),
    last_classify_time(0),
    classification_interval(5000),  // Default classification interval
    recalculate_threshold(total_blocks * pages_per_block / 20),  // Threshold for recalculating block scores
    use_experience_replay(use_exp_replay),
    mqsim_block_manager(block_manager),
    // 工作负载相关初始值
    workload_intensity(0.5),  // 默认中等水平
    last_intensity_update_time(0),
    // 其他动态参数
    dynamic_classification_interval(5000),
    dynamic_active_pool_size(4),
    max_replay_buffer_size(5000),
    target_blocks_per_class(4),
    hot_threshold(0.5),      // 初始热块阈值
    warm_threshold(0.25),    // 初始暖块阈值
    pages_per_block(pages_per_block) {
    
    // Set the singleton instance
    RLManager::instance = this;
    
    // Initialize two-layer RL agents with experience replay setting from constructor
    agent_layer1 = new RLAgent(NUM_STATES_LAYER1, NUM_ACTIONS_LAYER1, use_experience_replay);  // Block class prediction
    agent_layer2 = new RLAgent(NUM_STATES_LAYER2, NUM_ACTIONS_LAYER2, use_experience_replay);  // Page type prediction

    // Configure delayed Q-table updates for both agents (enabled by default)
    agent_layer1->use_delayed_update = true;
    agent_layer1->epoch_size = 2000;  // 2000 read operations per epoch
    agent_layer2->use_delayed_update = true;
    agent_layer2->epoch_size = 2000;  // 2000 read operations per epoch

    // Set legacy agent pointer for backward compatibility
    agent = agent_layer1;
    
    // Initialize parameters from XML configuration
    initialize_parameters_from_xml();
    
    // Initialize blocks
    blocks.resize(total_blocks);
    for (unsigned int i = 0; i < total_blocks; i++) {
        blocks[i] = new RLBlock(i);
        blocks[i]->pages.resize(pages_per_block, NO_LPA);
        blocks[i]->free_pages = pages_per_block;
        
        // Initialize all blocks as stateless initially
        blocks[i]->block_class = BlockClass::STATELESS;
    }
    
    // 获取SSD结构信息 - 从Block Manager获取通道、芯片、裸片和平面数量
    unsigned int channel_count = mqsim_block_manager->Get_channel_count();
    unsigned int chip_count = mqsim_block_manager->Get_chip_no_per_channel();
    unsigned int die_count = mqsim_block_manager->Get_die_no_per_chip();
    unsigned int plane_count = mqsim_block_manager->Get_plane_no_per_die();
    
    // 初始化平面级块池管理
    initialize_plane_block_lists(channel_count, chip_count, die_count, plane_count);
    
    // 固定每个块类别总共32个块，在页面类型间分配
    unsigned int blocks_per_class = 4;  // 固定为32个块
    unsigned int blocks_per_page_type = blocks_per_class / NUM_PAGE_TYPES;
    

    
    // 新的块分配策略：每个平面分为未写块、已写满块和块热度池
    // 修改为从0开始的连续编码分配方式
    unsigned int allocated_blocks = 0;

    // 计算每个平面可分配的块数
    unsigned int total_planes = channel_count * chip_count * die_count * plane_count;
    unsigned int blocks_per_plane = total_blocks / total_planes;

    // 为每个平面分配块 - 按照从0开始的连续编码方式
    unsigned int plane_index = 0;
    for (unsigned int channel = 0; channel < channel_count; channel++) {
        for (unsigned int chip = 0; chip < chip_count; chip++) {
            for (unsigned int die = 0; die < die_count; die++) {
                for (unsigned int plane = 0; plane < plane_count; plane++) {
                    PlaneID plane_id;
                    plane_id.channel_id = channel;
                    plane_id.chip_id = chip;
                    plane_id.die_id = die;
                    plane_id.plane_id = plane;

                    PlaneBlockList& plane_list = plane_block_lists[plane_id];
                    unsigned int plane_allocated_blocks = 0;

                    // 计算当前平面的起始块ID（从0开始连续编码）
                    unsigned int plane_start_block_id = plane_index * blocks_per_plane;

                    // 为每个块类别（热、暖、冷）分配固定12个可供写入的块到LP链表
                    for (int class_idx = 0; class_idx < NUM_BLOCK_CLASSES && allocated_blocks < total_blocks; class_idx++) {
                        // 每个块类型维护固定12个可供写入的块，全部初始化到LP链表
                        unsigned int blocks_to_allocate = std::min(1u,
                                                                 std::min(blocks_per_plane - plane_allocated_blocks,
                                                                        total_blocks - allocated_blocks));

            for (unsigned int i = 0; i < blocks_to_allocate && allocated_blocks < total_blocks; i++) {
                // 使用从0开始的连续编码方式分配块ID
                unsigned int block_index = plane_start_block_id + plane_allocated_blocks;
                if (block_index >= total_blocks) break;

                RLBlock* block = blocks[block_index];

                // 设置块的平面ID和类别
                block->channel_id = plane_id.channel_id;
                block->chip_id = plane_id.chip_id;
                block->die_id = plane_id.die_id;
                block->plane_id = plane_id.plane_id;
                block->block_class = static_cast<BlockClass>(class_idx);

                // 全新的块第一个待写页面肯定是LP，所以全部放入LP链表
                plane_list.block_lists[class_idx][0].blocks.push_back(block);
                allocated_blocks++;
                plane_allocated_blocks++;
            }
        }

        // 剩余块放入平面的未写块池
        unsigned int remaining_blocks = std::min(blocks_per_plane - plane_allocated_blocks,
                                               total_blocks - allocated_blocks);

        for (unsigned int i = 0; i < remaining_blocks && allocated_blocks < total_blocks; i++) {
            // 使用从0开始的连续编码方式分配块ID
            unsigned int block_index = plane_start_block_id + plane_allocated_blocks;
            if (block_index >= total_blocks) break;

            RLBlock* block = blocks[block_index];

            // 设置块的平面ID
            block->channel_id = plane_id.channel_id;
            block->chip_id = plane_id.chip_id;
            block->die_id = plane_id.die_id;
            block->plane_id = plane_id.plane_id;

            // 将块添加到平面的未写块池
            plane_list.unwritten_blocks.push_back(block);
            allocated_blocks++;
            plane_allocated_blocks++;
        }

        // 初始化已写满块池为空（根据需求）
        plane_list.full_blocks.clear();

        // 移动到下一个平面
        plane_index++;
                }
            }
        }
    }
    
    // 剩余块添加到全局未写块池（保持向后兼容性）
    stateless_blocks.reserve(total_blocks - allocated_blocks);
    for (unsigned int i = allocated_blocks; i < total_blocks; i++) {
        stateless_blocks.push_back(blocks[i]);
    }
    
    // Reserve space for full blocks
    full_blocks.reserve(total_blocks);
    
    
    // 初始化动态状态分类阈值
    state_thresholds.dynamic_access_count_threshold = 10;  // 初始值设为中间值
    state_thresholds.dynamic_access_interval_threshold = 6 * 3.6e12;  // 初始值设为6小时

    // Get TRE module reference if not already set
    if (tre_module == nullptr) {
        // Try to get reference from NVM_PHY_ONFI_NVDDR2
        tre_module = NVM_PHY_ONFI_NVDDR2::tre_module;

        // TRE module connection (logging removed)
    }
}

RLManager::~RLManager() {
    // Clean up blocks
    for (auto block : blocks) {
        delete block;
    }
    
    // Clean up agent
    delete agent;
}

void RLManager::initialize_parameters_from_xml() {
    // Get TRE module reference if not already set
    if (tre_module == nullptr) {
        // Try to get reference from NVM_PHY_ONFI_NVDDR2
        tre_module = NVM_PHY_ONFI_NVDDR2::tre_module;
    }
    
    // Initialize RL parameters with hardcoded values
    agent->epsilon = 0.8;
    agent->alpha = 0.3;
    agent->gamma = 0.8;
    agent->min_epsilon = 0.01;
    agent->epsilon_decay = 0.995;
    agent->min_replay_size = 300;
    agent->replay_batch_size = 16;
    max_replay_buffer_size = 5000;

    // 写死的参数值
    target_blocks_per_class = 4;
    classification_interval = 50000;
    tre_threshold = 0.005;
    hot_threshold = 0.5;
    warm_threshold = 0.25;
    enable_write_logging = false;
    enable_io_stats_logging = false;
    enable_classification_logging = false;

    // 状态分类阈值
    state_thresholds.fixed_access_count_thresholds[0] = 2;
    state_thresholds.fixed_access_count_thresholds[1] = 4;
    state_thresholds.fixed_access_interval_threshold = 21600000000000; // 6 hours in nanoseconds
    state_thresholds.dynamic_access_count_threshold = 10;
    state_thresholds.dynamic_access_interval_threshold = 21600000000000; // 6 hours in nanoseconds

    // 从ssdconfig.xml配置文件读取RL模块的关键参数

    // 统一设置max_pec为3000（固定值）
    max_pec = 3000;

    // 读取统一的基础PEC值，赋值给rl_base_pec变量
    // 这个值用于RL奖励计算和块分类
    rl_base_pec = Device_Parameter_Set::Base_PEC;

    // 读取RL模块的动态状态分类开关，赋值给use_dynamic_state_classification变量
    // true表示使用动态阈值进行状态分类，false表示使用固定阈值
    if (Device_Parameter_Set::RL_Use_Dynamic_State_Classification.HasValue) {
        use_dynamic_state_classification = Device_Parameter_Set::RL_Use_Dynamic_State_Classification.Value;
    } else {
        use_dynamic_state_classification = false;  // 默认值：使用固定阈值
    }
    
    // 更新所有已创建的RLBlock的base_pec值
    for (auto& plane_pair : plane_block_lists) {
        PlaneBlockList& plane_list = plane_pair.second;

        // 更新有状态块的base_pec
        for (int class_idx = 0; class_idx < NUM_BLOCK_CLASSES; class_idx++) {
            for (int page_type_idx = 0; page_type_idx < NUM_PAGE_TYPES; page_type_idx++) {
                for (auto& block : plane_list.block_lists[class_idx][page_type_idx].blocks) {
                    block->base_pec = rl_base_pec;
                }
            }
        }

        // 更新未写块的base_pec
        for (auto& block : plane_list.unwritten_blocks) {
            block->base_pec = rl_base_pec;
        }
    }

    // Startup initialization completed (logging removed)
}

int RLManager::discretize_state(LPA_type lpa, sim_time_type current_time) {
    auto& info = page_info[lpa];
    auto& access_timestamps = info.access_timestamps;
    
    // Time conversion constant
    const sim_time_type NS_PER_HOUR = 3.6e12;  // 1 hour = 3.6 × 10^12 ns
    
    // 根据XML配置确定使用固定策略或动态策略
    int long_term_count = 0;
    int weighted_access_interval = 0;  // 合并后的加权访问间隔状态

    // 1. 第一个状态：长期访问计数（3个维度值）
    // 分类间隔方式：根据访问次数进行分类
    // - 固定策略：使用XML配置的固定阈值
    // - 动态策略：使用动态更新的阈值，阈值/2和阈值作为分界点
    const size_t access_count = access_timestamps.size();

    // 根据配置选择使用的访问计数阈值
    if (!use_dynamic_state_classification) {
        // 固定值策略：使用固定阈值（从XML读取的配置）
        if (access_count < state_thresholds.fixed_access_count_thresholds[0]) {
            long_term_count = 0;  // 低频访问
        } else if (access_count < state_thresholds.fixed_access_count_thresholds[1]) {
            long_term_count = 1;  // 中频访问
        } else {
            long_term_count = 2;  // 高频访问
        }
    } else {
        // 动态策略：根据动态更新的阈值 state_thresholds.dynamic_access_count_threshold
        unsigned int threshold = state_thresholds.dynamic_access_count_threshold;
        if (access_count < threshold / 2) {
            long_term_count = 0;  // 低频访问
        } else if (access_count < threshold) {
            long_term_count = 1;  // 中频访问
        } else {
            long_term_count = 2;  // 高频访问
        }
    }
    
    // 2. 第二个状态：加权访问间隔（3个维度值）
    // 分类间隔方式：将移动平均间隔和当前间隔合并为加权访问间隔
    // 加权公式：加权访问间隔 = 0.7×当前间隔 + 0.3×移动平均间隔
    // 分类：短间隔(0)、中间隔(1)、长间隔(2)
    if (access_count >= 2) {
        // 计算最近一次访问间隔
        sim_time_type latest_interval = current_time - access_timestamps[access_timestamps.size() - 2];

        // 转换为小时，便于与阈值比较
        double latest_interval_hours = static_cast<double>(latest_interval) / NS_PER_HOUR;

        // 使用移动平均计算平均访问间隔
        if (page_interval_averages.find(lpa) == page_interval_averages.end()) {
            // 如果这是第一次计算这个LPA的移动平均，初始化
            page_interval_averages[lpa] = MovingAverage();
            page_interval_averages[lpa].update(latest_interval_hours);
        } else {
            // 更新现有的移动平均
            page_interval_averages[lpa].update(latest_interval_hours);
        }

        // 获取移动平均值
        double avg_interval_hours = page_interval_averages[lpa].value;

        // 计算加权访问间隔：0.7×当前 + 0.3×平均
        double weighted_interval_hours = 0.7 * latest_interval_hours + 0.3 * avg_interval_hours;

        // 根据策略选择使用的时间阈值
        sim_time_type interval_threshold = use_dynamic_state_classification ?
            state_thresholds.dynamic_access_interval_threshold :
            state_thresholds.fixed_access_interval_threshold;

        // 将区间转换为小时进行比较
        double threshold_hours = static_cast<double>(interval_threshold) / NS_PER_HOUR;

        // 加权访问间隔分类（3个维度）
        if (weighted_interval_hours < threshold_hours * 0.5) {
            weighted_access_interval = 0;  // 短间隔
        } else if (weighted_interval_hours < threshold_hours) {
            weighted_access_interval = 1;  // 中间隔
        } else {
            weighted_access_interval = 2;  // 长间隔
        }
    } else {
        // 如果访问次数少于2次，无法计算间隔，默认为长间隔
        weighted_access_interval = 2;
    }
    
    // 3. 第三个状态：上次分配块位置 Hot/Warm/Cold（3个维度值）
    // 分类间隔方式：根据上次分配的块类别进行分类
    // - HOT(0)：热块
    // - WARM(1)：温块
    // - COLD(2)：冷块
    int block_class_value = static_cast<int>(info.last_block_class);

    // 计算最终状态索引
    // 简化后的状态向量: <long_term_count, weighted_access_interval, block_class_value>
    // 新的维度大小: {3, 3, 3} = 27 states
    if (cache.initialized) {
        return fast_calculate_state_index_simplified(long_term_count, weighted_access_interval, block_class_value);
    } else {
        std::tuple<int, int, int> state_vector =
            std::make_tuple(long_term_count, weighted_access_interval, block_class_value);

        int state_index = calculate_state_index_layer1_simplified(state_vector);

        // Update state statistics
        state_vector_count_simplified[state_vector]++;
        state_index_count[state_index]++;

        return state_index;
    }
}



// Layer 1 RL: 简化的状态计算函数，适用于3维状态向量（移除邻居访问和页面大小）
// 用于预测块类别（HOT, WARM, COLD）
int RLManager::calculate_state_index_layer1_simplified(const std::tuple<int, int, int>& state_vector) {
    // Using pre-computed multipliers for each dimension
    // Based on dimension sizes: {3, 3, 3} = 27 states
    // long_term_count(3) * weighted_access_interval(3) * block_class_value(3)
    static const int multipliers[3] = {1, 3, 9};

    // Direct calculation without loops is faster
    return std::get<0>(state_vector) * multipliers[0] +
           std::get<1>(state_vector) * multipliers[1] +
           std::get<2>(state_vector) * multipliers[2];
}


// Layer 2 RL: 状态计算函数，基于预测的块类别和该类别活跃块的平均idle时间
// 用于预测页面类型（LP, MP, UP）
int RLManager::calculate_state_index_layer2(BlockClass predicted_block_class, const PlaneID& plane_id) {
    // 计算指定块类别的平均idle时间
    double avg_idle_time = calculate_average_idle_time(plane_id, predicted_block_class);

    // 将平均idle时间离散化为3个类别（与Layer1的间隔时间策略一致）
    int idle_time_category = discretize_idle_time(avg_idle_time);

    // 状态索引 = 块类别(3) * 平均idle时间类别(3)
    // 状态空间: {3 block classes} * {3 idle time categories} = 9 states
    return static_cast<int>(predicted_block_class) * 3 + idle_time_category;
}


// 检查邻居LPA是否在最近访问过
bool RLManager::check_neighbor_access(LPA_type lpa) {
    unsigned int range = state_thresholds.neighbor_range;

    // 检查lpa±range范围内是否有被访问过的LPA
    for (unsigned int offset = 1; offset <= range; offset++) {
        // 检查lpa-offset和lpa+offset
        if (recent_accessed_lpas.find(lpa - offset) != recent_accessed_lpas.end() ||
            recent_accessed_lpas.find(lpa + offset) != recent_accessed_lpas.end()) {
            return true;
        }
    }
    return false;
}

// 更新邻居访问跟踪
void RLManager::update_neighbor_access_tracking(LPA_type lpa) {
    // 添加当前LPA到最近访问集合
    recent_accessed_lpas.insert(lpa);
    lpa_access_queue.push(lpa);

    // 如果队列超过窗口大小，移除最旧的访问记录
    while (lpa_access_queue.size() > neighbor_io_window) {
        LPA_type old_lpa = lpa_access_queue.front();
        lpa_access_queue.pop();
        recent_accessed_lpas.erase(old_lpa);
    }
}

// 动态更新邻居范围
void RLManager::update_dynamic_neighbor_range() {
    if (!use_dynamic_state_classification) {
        state_thresholds.neighbor_range = 5;  // 固定策略下邻居范围为5
        return;
    }

    // 动态策略：根据PEC和工作负载强度调整邻居范围
    double pec_ratio = static_cast<double>(rl_base_pec) / 3000.0;  // PEC负相关
    double workload_factor = workload_intensity;  // 工作负载正相关

    // 结合PEC和工作负载强度，范围在1到10之间
    double combined_factor = (1.0 - pec_ratio) * 0.5 + workload_factor * 0.5;
    unsigned int new_range = std::max(1u, std::min(10u, static_cast<unsigned int>(1 + combined_factor * 9)));

    state_thresholds.neighbor_range = new_range;
}

// Layer 1 RL: 获取可用块类别掩码（mask掉没有可供写入的块类型）
std::vector<bool> RLManager::get_available_block_classes_mask(const PlaneID& plane_id) {
    std::vector<bool> mask(NUM_ACTIONS_LAYER1, false);

    auto plane_it = plane_block_lists.find(plane_id);
    if (plane_it == plane_block_lists.end()) {
        // 如果平面不存在，返回全false掩码
        return mask;
    }

    const PlaneBlockList& plane_list = plane_it->second;

    // 检查每个块类别是否有可供写入的块（从九个链表中检查）
    for (int block_class_idx = 0; block_class_idx < NUM_BLOCK_CLASSES; block_class_idx++) {
        bool has_available_block = false;

        // 检查该块类别的所有页面类型链表是否有可用块
        for (int page_type_idx = 0; page_type_idx < NUM_PAGE_TYPES; page_type_idx++) {
            const auto& block_list = plane_list.block_lists[block_class_idx][page_type_idx];

            // 检查链表中是否有非满的块
            for (const auto& block : block_list.blocks) {
                if (block->free_pages > 0) {
                    has_available_block = true;
                    break;
                }
            }
            if (has_available_block) break;
        }

        // 掩码机制只检查9个链表中的情况，不考虑未写块池
        // 未写块池的补充逻辑在块写满后自动触发，不在掩码判断时考虑

        mask[block_class_idx] = has_available_block;
    }

    return mask;
}

// Layer 2 RL: 获取可用页面类型掩码（mask掉没有可供写入的页面类型）
std::vector<bool> RLManager::get_available_page_types_mask(const PlaneID& plane_id, BlockClass block_class) {
    std::vector<bool> mask(NUM_ACTIONS_LAYER2, false);

    auto plane_it = plane_block_lists.find(plane_id);
    if (plane_it == plane_block_lists.end()) {
        // 如果平面不存在，返回全false掩码
        return mask;
    }

    const PlaneBlockList& plane_list = plane_it->second;
    int block_class_idx = static_cast<int>(block_class);

    // 检查每个页面类型是否有可供写入的块
    for (int page_type_idx = 0; page_type_idx < NUM_PAGE_TYPES; page_type_idx++) {
        bool page_type_available = false;

        // 首先检查指定块类型的对应页面类型链表
        const auto& block_list = plane_list.block_lists[block_class_idx][page_type_idx];
        for (const auto& block : block_list.blocks) {
            if (block->free_pages > 0) {
                page_type_available = true;
                break;
            }
        }


        mask[page_type_idx] = page_type_available;
    }

    return mask;
}

// 获取覆盖所有块类型的页面类型掩码（从九个链表中检查）
std::vector<bool> RLManager::get_comprehensive_page_types_mask(const PlaneID& plane_id) {
    std::vector<bool> page_type_mask(NUM_ACTIONS_LAYER2, false);

    // 检查平面是否存在
    auto plane_it = plane_block_lists.find(plane_id);
    if (plane_it == plane_block_lists.end()) {
        return page_type_mask;  // 平面不存在，返回全false
    }

    const PlaneBlockList& plane_list = plane_it->second;

    // 检查每个页面类型是否在九个链表中的任何一个可用
    for (int page_type_idx = 0; page_type_idx < NUM_PAGE_TYPES; page_type_idx++) {
        bool page_type_available = false;

        // 遍历所有块类型（HOT, WARM, COLD）的对应页面类型链表
        for (int class_idx = 0; class_idx < NUM_BLOCK_CLASSES; class_idx++) {
            const auto& target_list = plane_list.block_lists[class_idx][page_type_idx];

            // 检查该链表是否有可用块
            if (!target_list.blocks.empty()) {
                // 检查是否有非满的块
                for (const auto& block : target_list.blocks) {
                    if (block->free_pages > 0) {
                        page_type_available = true;
                        break;
                    }
                }
                if (page_type_available) break;  // 找到一个可用的就足够了
            }
        }

        // 掩码机制只检查9个链表中的情况，不考虑未写块池
        // 未写块池的补充逻辑在块写满后自动触发，不在掩码判断时考虑

        page_type_mask[page_type_idx] = page_type_available;
    }

    return page_type_mask;
}

// 计算指定块类别的平均idle时间
double RLManager::calculate_average_idle_time(const PlaneID& plane_id, BlockClass block_class) {
    auto plane_it = plane_block_lists.find(plane_id);
    if (plane_it == plane_block_lists.end()) {
        return 0.0;  // 如果平面不存在，返回0
    }

    const PlaneBlockList& plane_list = plane_it->second;
    int block_class_idx = static_cast<int>(block_class);

    double total_idle_time = 0.0;
    unsigned int block_count = 0;

    // 遍历该块类别的所有页面类型
    for (int page_type_idx = 0; page_type_idx < NUM_PAGE_TYPES; page_type_idx++) {
        const auto& block_list = plane_list.block_lists[block_class_idx][page_type_idx];

        // 计算所有活跃块的idle时间
        for (const auto& block : block_list.blocks) {
            if (block->free_pages > 0) {  // 只考虑活跃块
                sim_time_type idle_time = current_time - block->last_access_time;
                total_idle_time += static_cast<double>(idle_time);
                block_count++;
            }
        }
    }

    return (block_count > 0) ? (total_idle_time / block_count) : 0.0;
}

// 将平均idle时间离散化为3个类别（与Layer1的间隔时间策略一致）
int RLManager::discretize_idle_time(double avg_idle_time) {
    // 时间转换常量
    const sim_time_type NS_PER_HOUR = 3.6e12;  // 1小时 = 3.6 × 10^12 纳秒

    // 根据动静态策略确定阈值
    sim_time_type threshold;
    if (use_dynamic_state_classification) {
        threshold = state_thresholds.dynamic_access_interval_threshold;
    } else {
        threshold = state_thresholds.fixed_access_interval_threshold;
    }

    // 将平均idle时间分为3个类别
    if (avg_idle_time < threshold / 2.0) {
        return 0;  // 短间隔
    } else if (avg_idle_time < threshold) {
        return 1;  // 中间隔
    } else {
        return 2;  // 长间隔
    }
}






// Update workload intensity based on recent IO operations
void RLManager::update_workload_intensity(sim_time_type current_time) {
    // 固定更新周期为50000个IO (同时包含读和写请求)
    const unsigned int UPDATE_PERIOD = 50000;
    
    // 如果是第一次更新（上次更新时间为0），则初始化
    if (last_intensity_update_time == 0) {
        // 初始化时间戳
        last_intensity_update_time = current_time;
        // 第一次调用时，还没有足够数据计算，设置为当前强度
        double cur_intensity = (static_cast<double>(UPDATE_PERIOD) / current_time) * 1e9;
    
        // 归一化，假设10000 IO/s是高强度(1.0)
        cur_intensity = std::min(1.0, cur_intensity / 10000.0);
        workload_intensity = cur_intensity; 
        return;
    }
    
    // 计算当前时间窗口内的工作负载强度
    sim_time_type time_diff = current_time - last_intensity_update_time;
    if (time_diff <= 0) {
        // 时间差异太小或无效，保持当前强度不变
        return;
    }
    
    // 计算当前IO速率 = IO数量(读+写) / 时间差
    // total_io_count已经同时包含了读和写请求的计数
    double current_intensity = (static_cast<double>(UPDATE_PERIOD) / time_diff) * 1e9;
    
    // 归一化，假设10000 IO/s是高强度(1.0)
    current_intensity = std::min(1.0, current_intensity / 10000.0);
    
    // 使用移动平均计算最终工作负载强度
    // alpha参数决定新样本的权重：0.3表示新样本占30%，旧值占70%
    const double alpha = 0.3;
    workload_intensity = alpha * current_intensity + (1.0 - alpha) * workload_intensity;
    
    // 更新最后更新时间
    last_intensity_update_time = current_time;
    
    // 调试输出
    /*
    if (total_io_count % (UPDATE_PERIOD * 10) == 0) {
        std::cout << "[RL工作负载强度更新] IO计数:" << total_io_count 
                  << " 读计数:" << total_read_count
                  << " 写计数:" << write_count
                  << " 当前强度:" << current_intensity
                  << " 综合强度:" << workload_intensity
                  << " 时间差(ms):" << time_diff/1e6 
                  << std::endl;
    }
    */
}

// Update classification parameters
void RLManager::update_classification_parameters() {
    // 更新工作负载强度 - 确保每次调用update_classification_parameters时都更新
    update_workload_intensity(current_time);
    
    // 计算有效PEC - 在实际系统中，应该获取SSD的实际PEC值
    // 使用统一的3000作为最大PEC值进行归一化
    const double max_pec_value = 3000.0;

    // 获取当前平均PEC（归一化到0-1范围）
    double pec_ratio = std::min(1.0, rl_base_pec / max_pec_value);
    double pec_factor = 1.0 - pec_ratio; // PEC越大，因子越小（负相关）
    
    // 工作负载强度已经在update_workload_intensity更新，范围0-1
    
    // 结合PEC和工作负载强度，各占0.5权重
    double combined_weight = pec_factor * 0.5 + workload_intensity * 0.5;
    
    // 每隔5万个IO检查一次是否需要更新动态参数
    if (total_io_count % 50000 == 0) {
        // 动态调整热块和暖块的阈值
        hot_threshold = combined_weight;
        warm_threshold = hot_threshold * 0.5; // 暖块阈值为热块阈值的一半
        
        // 如果使用动态状态分类策略，更新状态阈值
        if (use_dynamic_state_classification) {
            // 存储旧的阈值用于比较
            unsigned int old_access_count_threshold = state_thresholds.dynamic_access_count_threshold;
            sim_time_type old_access_interval_threshold = state_thresholds.dynamic_access_interval_threshold;
            
            // 1. 动态调整访问计数阈值 (2-20次)
            // 访问计数阈值与combined_weight正相关：工作负载越重，访问计数阈值越大
            state_thresholds.dynamic_access_count_threshold = 2 + static_cast<unsigned int>(4.0 * combined_weight);
            
            // 2. 动态调整访问间隔阈值 (2-10小时)
            // 访问间隔阈值与combined_weight成反比：工作负载越重，访问间隔阈值越小
            int hours = 10 - static_cast<int>(8.0 * combined_weight);
            // 确保范围在2-10小时内
            hours = std::min(10, std::max(2, hours));
            state_thresholds.dynamic_access_interval_threshold = static_cast<sim_time_type>(hours * 3.6e12); // 转换为纳秒
            
            // 3. 调整邻居范围，随PEC和负载强度变化
            // 注释掉对邻居范围参数的动态调整，只保留计数阈值和访问间隔阈值的动态调整
            // update_neighbor_range_parameters();
            
            // Threshold update completed (logging removed)
        }
    }
    
    // 固定分类间隔为5万个IO
    dynamic_classification_interval = 50000;
    
    // 固定活跃池大小为32（每个温度类别）
    dynamic_active_pool_size = 4;
}

RLBlock* RLManager::select_block(BlockClass block_class) {
    const int class_idx = static_cast<int>(block_class);
    
    // Fast path: check if requested pool is empty
    if (block_pools[class_idx].empty()) {
        // Try to find any non-empty pool
        for (int i = 0; i < 3; i++) {  // Only check active block classes
            if (!block_pools[i].empty()) {
                // Recursive call with new class - this avoids code duplication
                return select_block(static_cast<BlockClass>(i));
            }
        }
        return nullptr;  // No blocks available
    }
    
    // Optimization: Cache the heap comparison function
    static const auto heap_compare = [](const std::pair<sim_time_type, flash_block_ID_type>& a, 
                                      const std::pair<sim_time_type, flash_block_ID_type>& b) {
        return a.first > b.first;  // Min-heap by access time
    };
    
    // Try to find a block with free space in the heap
    auto& time_heap = block_pools_by_access_time[class_idx];
    
    // Cache heap operations to avoid redundant work
    const size_t max_heap_checks = std::min(time_heap.size(), static_cast<size_t>(10));
    
    // Use a small fixed-size array on stack instead of dynamic allocation for better performance
    std::array<std::pair<sim_time_type, flash_block_ID_type>, 10> saved_entries;
    size_t saved_count = 0;
    
    // Process up to 10 elements from the top of the heap
    for (size_t i = 0; i < max_heap_checks; i++) {
        if (time_heap.empty()) break;
        
        // Get top element
        const auto& entry = time_heap.front();
        RLBlock* block = blocks[entry.second];
        
        // Remove it from heap
        std::pop_heap(time_heap.begin(), time_heap.end(), heap_compare);
        time_heap.pop_back();
        
        // Check if valid
        if (block->block_class == static_cast<BlockClass>(class_idx)) {
            // Save for re-adding later
            saved_entries[saved_count++] = {block->last_access_time, entry.second};
            
            // If it has free space, re-add entries and return it
            if (block->free_pages > 0) {
                // Re-add all saved entries
                for (size_t j = 0; j < saved_count; j++) {
                    time_heap.push_back(saved_entries[j]);
                    std::push_heap(time_heap.begin(), time_heap.end(), heap_compare);
                }
                return block;
            }
        }
    }
    
    // Re-add all saved entries we processed
    for (size_t i = 0; i < saved_count; i++) {
        time_heap.push_back(saved_entries[i]);
        std::push_heap(time_heap.begin(), time_heap.end(), heap_compare);
    }
    
    // Fast path for small pools: direct iteration is faster than collecting and min_element
    if (block_pools[class_idx].size() <= 20) {
        RLBlock* best_block = nullptr;
        sim_time_type earliest_time = std::numeric_limits<sim_time_type>::max();
        
        for (auto block : block_pools[class_idx]) {
            if (block->free_pages > 0 && block->last_access_time < earliest_time) {
                earliest_time = block->last_access_time;
                best_block = block;
            }
        }
        
        if (best_block) return best_block;
    } else {
        // For larger pools: find blocks with free space
        std::vector<RLBlock*> valid_blocks;
        valid_blocks.reserve(std::min(block_pools[class_idx].size(), static_cast<size_t>(20)));
        
        for (auto block : block_pools[class_idx]) {
            if (block->free_pages > 0) {
                valid_blocks.push_back(block);
                // Early exit after finding some candidates
                if (valid_blocks.size() >= 20) break;
            }
        }
        
        if (!valid_blocks.empty()) {
            // Find block with earliest access time
            auto min_block = std::min_element(valid_blocks.begin(), valid_blocks.end(),
                                            [](const RLBlock* a, const RLBlock* b) {
                                                return a->last_access_time < b->last_access_time;
                                            });
            return *min_block;
        }
    }
    
    return nullptr;  // No suitable block found
}

double RLManager::calculate_hotness(RLBlock* block) {
    // 修改为仅使用最近一次访问时间（块的空闲时间）作为分类依据
    // 与calculate_block_temperature_score保持一致的逻辑

    if (current_time <= block->last_access_time) {
        // 如果当前时间不大于最后访问时间，说明块刚被访问，给予最高得分
        return 1.0;
    }

    // 计算空闲时间（纳秒）
    sim_time_type idle_time = current_time - block->last_access_time;

    // 将空闲时间转换为小时，便于理解和调试
    const sim_time_type NS_PER_HOUR = 3.6e12;  // 1小时 = 3.6 × 10^12 纳秒
    double idle_time_hours = static_cast<double>(idle_time) / NS_PER_HOUR;

    // 使用指数衰减函数计算热度得分
    // 空闲时间越长，得分越低
    // 使用半衰期为1小时的指数衰减：score = exp(-idle_time_hours / 1.0)
    double hotness_score = std::exp(-idle_time_hours / 1.0);

    // 确保得分在[0, 1]范围内
    return std::max(0.0, std::min(1.0, hotness_score));
}

double RLManager::calculate_block_temperature_score(RLBlock* block) {
    // 修改为仅使用最近一次访问时间（块的空闲时间）作为分类依据
    // 空闲时间越短，温度得分越高（表示块越热）

    if (current_time <= block->last_access_time) {
        // 如果当前时间不大于最后访问时间，说明块刚被访问，给予最高得分
        return 1.0;
    }

    // 计算空闲时间（纳秒）
    sim_time_type idle_time = current_time - block->last_access_time;

    // 将空闲时间转换为小时，便于理解和调试
    const sim_time_type NS_PER_HOUR = 3.6e12;  // 1小时 = 3.6 × 10^12 纳秒
    double idle_time_hours = static_cast<double>(idle_time) / NS_PER_HOUR;

    // 使用指数衰减函数计算温度得分
    // 空闲时间越长，得分越低
    // 使用半衰期为1小时的指数衰减：score = exp(-idle_time_hours / 1.0)
    // 这样：
    // - 刚访问过的块（idle_time = 0）得分为 1.0（最热）
    // - 1小时前访问的块得分约为 0.37
    // - 2小时前访问的块得分约为 0.14
    // - 3小时前访问的块得分约为 0.05（最冷）
    double temperature_score = std::exp(-idle_time_hours / 1.0);

    // 确保得分在[0, 1]范围内
    return std::max(0.0, std::min(1.0, temperature_score));
}

// 联合评分机制：结合访问频率和idle时间
double RLManager::calculate_joint_score(RLBlock* block) {
    // 计算访问频率得分（0-1）
    double frequency_score = calculate_access_frequency_score(block);

    // 计算idle时间得分（0-1）- 复用现有的温度得分逻辑
    double idle_score = calculate_block_temperature_score(block);

    // 联合得分：两个指标等权重结合
    // 使用几何平均数确保两个指标都需要表现良好才能获得高分
    double joint_score = std::sqrt(frequency_score * idle_score);

    return std::max(0.0, std::min(1.0, joint_score));
}

// 计算块的访问频率得分（基于最近50000次IO）
double RLManager::calculate_access_frequency_score(RLBlock* block) {
    if (block == nullptr) {
        return 0.0;
    }

    // 清理过期的访问记录（超过50000次IO的记录）
    sim_time_type current_time = this->current_time;
    cleanup_old_access_records(current_time);

    // 计算该块在最近50000次IO中的访问次数
    unsigned int access_count = block->access_count_in_window;

    // 如果全局访问历史为空，返回0
    if (global_access_history.empty()) {
        return 0.0;
    }

    // 计算访问频率：该块访问次数 / 总访问次数
    double access_frequency = static_cast<double>(access_count) / global_access_history.size();

    // 使用对数函数将频率映射到0-1范围，避免极端值
    // 频率越高，得分越高
    double frequency_score = std::min(1.0, access_frequency * 10.0);  // 放大10倍以提高敏感度

    return std::max(0.0, frequency_score);
}

// 更新块的访问频率统计
void RLManager::update_block_access_frequency(flash_block_ID_type block_id, sim_time_type access_time) {
    // 添加到全局访问历史
    global_access_history.push_back(std::make_pair(access_time, block_id));

    // 维护窗口大小：保持最近50000次访问
    while (global_access_history.size() > ACCESS_FREQUENCY_WINDOW) {
        auto oldest_access = global_access_history.front();
        global_access_history.pop_front();

        // 减少对应块的窗口内访问计数
        if (oldest_access.second < blocks.size() && blocks[oldest_access.second] != nullptr) {
            if (blocks[oldest_access.second]->access_count_in_window > 0) {
                blocks[oldest_access.second]->access_count_in_window--;
            }
        }
    }

    // 增加当前块的窗口内访问计数
    if (block_id < blocks.size() && blocks[block_id] != nullptr) {
        blocks[block_id]->access_count_in_window++;
        blocks[block_id]->recent_access_times.push_back(access_time);

        // 维护块级别的访问时间队列（最多保留最近1000次访问）
        while (blocks[block_id]->recent_access_times.size() > 1000) {
            blocks[block_id]->recent_access_times.pop_front();
        }
    }
}

// 清理过期的访问记录
void RLManager::cleanup_old_access_records(sim_time_type current_time) {
    // 定期清理：每10000次IO清理一次
    static unsigned int cleanup_counter = 0;
    cleanup_counter++;

    if (cleanup_counter % 10000 != 0) {
        return;
    }

    // 清理超过时间窗口的访问记录（这里以时间为准，辅助IO次数窗口）
    const sim_time_type TIME_WINDOW = 24 * 3600 * 1e12;  // 24小时的纳秒数

    // 清理全局访问历史中的过期记录
    while (!global_access_history.empty() &&
           (current_time - global_access_history.front().first) > TIME_WINDOW) {
        auto oldest_access = global_access_history.front();
        global_access_history.pop_front();

        // 减少对应块的窗口内访问计数
        if (oldest_access.second < blocks.size() && blocks[oldest_access.second] != nullptr) {
            if (blocks[oldest_access.second]->access_count_in_window > 0) {
                blocks[oldest_access.second]->access_count_in_window--;
            }
        }
    }

    // 清理各个块的访问时间队列
    for (auto& block : blocks) {
        if (block != nullptr) {
            while (!block->recent_access_times.empty() &&
                   (current_time - block->recent_access_times.front()) > TIME_WINDOW) {
                block->recent_access_times.pop_front();
            }
        }
    }
}

void RLManager::update_block_access_stats(flash_block_ID_type block_id) {
    total_io_count++;
    blocks[block_id]->total_access_count++;
    
    // Add to recent accesses queue
    recent_block_accesses.push_back(block_id);
    if (recent_block_accesses.size() > 4000) {
        recent_block_accesses.pop_front();
    }
}

// Helper functions for PageType conversion
std::string RLManager::page_type_to_string(PageType type) {
    switch (type) {
        case PageType::LP: return "LP";
        case PageType::MP: return "MP";
        case PageType::UP: return "UP";
        default: return "Unknown";
    }
}

// 函数已移除，页面类型应由物理页地址决定

// 修改为获取物理页地址的页面类型
PageType RLManager::page_id_to_page_type(const NVM::FlashMemory::Physical_Page_Address& physical_address) {
    // 根据块内页序号(Page_ID)确定页面类型
    // 在TLC闪存中，页类型由页在块内的位置决定
    switch (physical_address.PageID % 3) {
        case 0: return PageType::LP; // 低页(LSB)
        case 1: return PageType::MP; // 中页(CSB)
        case 2: return PageType::UP; // 高页(MSB)
        default: return PageType::LP; // 不应该到达这里
    }
}

// 注释掉calculate_rber函数的实现，保留函数声明
double RLManager::calculate_rber(sim_time_type idle_time, PageType page_type, unsigned int pec)
{
    double T = idle_time / 3.6e12; // 1h = 3.6e12 ns
    
    // Protection against invalid time values
    if (T <= 0) {
        return 0.0;
    }
    
    
    // Take natural logarithm of time
    //double ln_T = log(T);
    // Set page type dummy variables
    double PageType0 = 0, PageType1 = 0;
    switch (page_type) {
        case PageType::LP: // Lower Page (LSB/SLC)
            PageType0 = 1;
            PageType1 = 0;
            break;
        case PageType::MP: // Middle Page (CSB/MLC)
            PageType0 = 0;
            PageType1 = 1;
            break;
        case PageType::UP: // Upper Page (MSB/TLC)
            PageType0 = 0;
            PageType1 = 0;
            break;
    }

    // Normalize PEC
    const double MIN_PEC = 0;
    // 统一使用3000作为最大PEC值进行归一化
    const double MAX_PEC = 3000;
    int original_pec = pec;


    // test
    //PageType0 = 1;
    //PageType1 = 0;
    //original_pec = 1800;
    //T = 4;

    double normalized_pec = (original_pec - MIN_PEC) / (MAX_PEC - MIN_PEC);


    // Calculate RBER based on PEC range
    if (original_pec < 0) {
        // Low PEC formula
        double Ylow = (0.000112873013190101 + 0.000606593520728399 * (normalized_pec) 
            + 0.00000198974163505961 * PageType0 
            + 0.000000112595766534370 * PageType1) * T 
            + (0.00000000600695903183062 
                + 0.00400912206217582 * (normalized_pec)
                + 0.00206828176514910 * PageType0 
                + 0.00246339421409020 * PageType1);

        //std::cout<<"Ylow"<<Ylow<<std::endl;
        //double Ylow = (0.00024521 + 0.00045251 * normalized_pec - 0.000076135 * PageType0 - 0.000092886 * PageType1) * T +(-0.0019272 + 0.006457 * normalized_pec + 0.0030701 * PageType0 + 0.0036465 * PageType1);
        return Ylow;
    } 
    else if (original_pec >= 5000) {
        // High PEC formula
        double exp_factor = (-0.0821082610721852 
                + 0.128958140284970 * (normalized_pec)
                - 0.0207336236364037 * PageType0 
                + 0.00903729782280047 * PageType1) * T;
        double Yhigh = (-0.00519411156037596 
            + 0.00114498442027409 * (normalized_pec)
            + 0.00180140363821254 * PageType0 
            + 0.00183569135182064 * PageType1 
            + 0.00116971163944702 * (normalized_pec) * T)
            * exp(exp_factor) 
            + 0.00603119862706745;
        return Yhigh;
    }
    else {
        // Mid PEC range, use weighted average
        double w_epc = (original_pec - 0) / 3000.0;
        
        // Calculate Ylow
        double Ylow = (0.000112873013190101 + 0.000606593520728399 * (normalized_pec) 
            + 0.00000198974163505961 * PageType0 
            + 0.000000112595766534370 * PageType1) * T 
            + (0.00000000600695903183062 
                + 0.00400912206217582 * (normalized_pec)
                + 0.00206828176514910 * PageType0 
                + 0.00246339421409020 * PageType1);
        
        // Calculate Yhigh
        double exp_factor = (-0.0821082610721852 
                + 0.128958140284970 * (normalized_pec)
                - 0.0207336236364037 * PageType0 
                + 0.00903729782280047 * PageType1) * T;
        double Yhigh = (-0.00519411156037596 
            + 0.00114498442027409 * (normalized_pec)
            + 0.00180140363821254 * PageType0 
            + 0.00183569135182064 * PageType1 
            + 0.00116971163944702 * (normalized_pec) * T)
            * exp(exp_factor) 
            + 0.00603119862706745;

        double middle = (1 - w_epc) * Ylow + w_epc * Yhigh; 
        // Weighted average
        return middle;
    }
}

// 注释掉calculate_dr_rber函数的实现，保留函数声明
double RLManager::calculate_dr_rber(sim_time_type idle_time, PageType page_type, unsigned int pec)
{
    /* 注释掉DR-RBER计算逻辑，使用TRE模块的计算结果
    double T = idle_time / 3.6e12; // 1h = 3.6e12 ns
    
    // Protection against invalid time values
    if (T <= 0) {
        return 0.0;
    }
    
    
    // Take natural logarithm of time
    //double ln_T = log(T);
    // Set page type dummy variables
    double PageType0 = 0, PageType1 = 0;
    switch (page_type) {
        case PageType::LP: // Lower Page (LSB/SLC)
            PageType0 = 1;
            PageType1 = 0;
            break;
        case PageType::MP: // Middle Page (CSB/MLC)
            PageType0 = 0;
            PageType1 = 1;
            break;
        case PageType::UP: // Upper Page (MSB/TLC)
            PageType0 = 0;
            PageType1 = 0;
            break;
    }

    // test
    //PageType0 = 0;
    //PageType1 = 0;
    //T = 26;
    //pec = 2900;

    // Normalize PEC
    const double MIN_PEC = 0;
    const double MAX_PEC = 3000;
    double normalized_pec = (pec - MIN_PEC) / (MAX_PEC - MIN_PEC);

    
    // 时间指数部分
    double exponent_T = 0.242263175096745 
                    + 0.682537551950320 * normalized_pec
                    + 0.0606063129174000 * PageType0
                    + 0.178996816279455 * PageType1;
    
    // 指数项部分
    double exp_term = -7.48335707556119 
                    + 0.518655053076237 * normalized_pec
                    - 0.156103044259520 * PageType0
                    - 0.482358482458141 * PageType1;
    
    // 计算最终rBER
    double dr_rber = pow(T, exponent_T) * exp(exp_term);   
    //std::cout<<"rber"<<rber<<std::endl;
    return dr_rber;
    */
    
    // 使用默认返回值，实际上将使用TRE模块的计算结果
    return 0.0;
}

// 注释掉calculate_delay函数的实现，保留函数声明
uint64_t RLManager::calculate_delay(double rber)
{
 
    const double solid_factor = 0.6;
    double delay = 0;
    double rber_off = 0.0;
    // Calculate delay based on RBER
    if (rber < 0.005-rber_off) {
        delay = 85;
    }
    else if (rber < 0.006-rber_off) {
        delay = 85 + 109;
    }
    else if (rber < 0.008-rber_off) {
        delay = 194 + 133;
    }
    else if (rber < 0.009-rber_off) {
        delay = 327 + 157;
    }
    else if (rber < 0.01-rber_off) {
        delay = 484 + 181;
    }
    else if (rber < 0.012-rber_off) {
        delay = 665 + 205;
    }
    else if (rber <= 0.013) {
        delay = 870 + 229;
    }
    else { // rber > 0.013
        delay = 1099;
    }

    return static_cast<uint64_t>(delay);
}

// 注释掉calculate_reward函数的实现，保留函数声明
double RLManager::calculate_reward(double rber) {
    /* 注释掉基于RBER的奖励计算逻辑，使用TRE模块的计算结果
    // Use solid state factor from XML configuration
    double solid_factor = this->solid_factor;
    
    // Calculate delay based on RBER value
    double delay;
    
    // Use TRE threshold from XML configuration
    if (rber < tre_threshold) {
        delay = 85 * solid_factor + (rber / tre_threshold) * 85 * (1 - solid_factor);
        // Map to [3, 0] range - lower RBER gives higher reward
        double max_delay_in_range = 85;
        double normalized_reward = 3 - (3 * delay / max_delay_in_range);
        return std::max(0.0, std::min(3.0, normalized_reward));
    }
    else if (rber < 0.006) {
        delay = 85 + 109 * solid_factor + ((rber - tre_threshold) / 0.001) * 109 * (1 - solid_factor);
    }
    else if (rber < 0.008) {
        delay = 194 + 133 * solid_factor + ((rber - 0.006) / 0.002) * 133 * (1 - solid_factor);
    }
    else if (rber < 0.009) {
        delay = 327 + 157 * solid_factor + ((rber - 0.008) / 0.001) * 157 * (1 - solid_factor);
    }
    else if (rber < 0.01) {
        delay = 484 + 181 * solid_factor + ((rber - 0.009) / 0.001) * 181 * (1 - solid_factor);
    }
    else if (rber < 0.012) {
        delay = 665 + 205 * solid_factor + ((rber - 0.01) / 0.002) * 205 * (1 - solid_factor);
    }
    else if (rber <= 0.013) {
        delay = 870 + 229 * solid_factor + ((rber - 0.012) / 0.001) * 229 * (1 - solid_factor);
    }
    else { // rber > 0.013
        delay = 1099 + ((rber - 0.013) / 0.001) * 229;
    }
    
    // For RBER in [0.005, 0.013], map delay to reward range [0, -6]
    if (tre_threshold <= rber && rber <= 0.013) {
        double min_delay = 85;
        double max_delay = 1099;
        double normalized_reward = -6 * (delay - min_delay) / (max_delay - min_delay);
        return std::max(-6.0, std::min(0.0, normalized_reward));
    }
    
    // For RBER > 0.013, fixed reward of -7
    return -7.0;
    */
    
    // 使用默认返回值，实际上将使用基于TRE模块延时的奖励计算
    return 0.0;
}

// 注释掉reward_to_delay函数的实现，保留函数声明
double RLManager::reward_to_delay(double reward) {
    /* 注释掉奖励转延时的逻辑，使用TRE模块的计算结果
    // Convert reward back to delay (in microseconds)
    if (reward >= 0 && reward <= 3) {
        // [3, 0] -> [0, 85]
        return (3 - reward) * 85 / 3;
    }
    else if (reward < 0 && reward >= -6) {
        // [0, -6] -> [85, 1099]
        return 85 + (-reward) * (1099 - 85) / 6;
    }
    else { // reward < -6
        return 1099;  // Fixed delay for values beyond threshold
    }
    */
    
    // 使用默认返回值，实际上将使用TRE模块的延时
    return 0.0;
}

bool RLManager::process_write_request(LPA_type lpa, sim_time_type current_time, unsigned int num_pages, const NVM::FlashMemory::Physical_Page_Address& address, stream_id_type stream_id) {
    // 创建一个可变的地址副本
    NVM::FlashMemory::Physical_Page_Address mutable_address = address;
    
    // 直接调用新的allocate_page_based_on_rl方法，传递非const引用
    // 默认使用stream_id = 0，如果需要支持多流，这里应该传递正确的stream_id
    bool result = allocate_page_based_on_rl(lpa, current_time, num_pages, mutable_address, stream_id);
    // 将修改后的地址复制回const_cast后的原始地址，确保地址被正确传递回调用者
    if (result) {
        NVM::FlashMemory::Physical_Page_Address* non_const_address = const_cast<NVM::FlashMemory::Physical_Page_Address*>(&address);
        *non_const_address = mutable_address;
    }
    return result;
}

void RLManager::handle_block_erase(const NVM::FlashMemory::Physical_Page_Address& address, sim_time_type time) {
    flash_block_ID_type block_id = address.BlockID;
    
    // Check if block exists in our model
    if (block_id >= blocks.size()) {
        return;
    }
    
    // Get the block
    RLBlock* block = blocks[block_id];
    
    // Update current time
    current_time = time;
    
    
    // Clean up the page-to-block mapping for all pages in this block
    for (LPA_type lpa : block->pages) {
        if (lpa != NO_LPA) {
            // 清除LPA到块的映射
            page_to_block.erase(lpa);
            
            // 清除LPA到PPA的映射
            lpa_to_ppa_mapping.erase(lpa);
            
        }
    }
    
    // Remember the block class before erasure
    BlockClass old_block_class = block->block_class;
    
    // Find and remove the block from its current list
    if (old_block_class != BlockClass::STATELESS) {
        int class_idx = static_cast<int>(old_block_class);
        
        // Find which page type list contains this block
        for (int page_type_idx = 0; page_type_idx < NUM_PAGE_TYPES; page_type_idx++) {
            auto& list = block_lists[class_idx][page_type_idx];
            auto it = std::find(list.blocks.begin(), list.blocks.end(), block);
            
            if (it != list.blocks.end()) {
                // Remove from list
                list.blocks.erase(it);
                break;
            }
            }
    } else {
        // Remove from stateless blocks if present
        auto it = std::find(stateless_blocks.begin(), stateless_blocks.end(), block);
        if (it != stateless_blocks.end()) {
            stateless_blocks.erase(it);
        }
    }
    
    // Check if the block is in full blocks list
    auto full_it = std::find(full_blocks.begin(), full_blocks.end(), block);
    if (full_it != full_blocks.end()) {
        full_blocks.erase(full_it);
    }
    
    // Erase the block in our model
    block->erase_block(time);
    
    // Set block class to stateless
        block->block_class = BlockClass::STATELESS;
        
        // Add to stateless pool
    stateless_blocks.push_back(block);
    
    // Removed erase operation logging
    
    // 被擦除的块一定是写满的块，不是活跃块池中的块
    // 这里不需要添加新块，因为写满的块已经不在活跃块池中了
    // 活跃块池的大小维护是在写操作时完成的
}

// Update process_read_request function to use TRE module for delay calculation
std::map<std::string, double> RLManager::process_read_request(LPA_type lpa, sim_time_type current_time, NVM_Transaction_Flash_RD* read_transaction, uint64_t delay_us_rber) {
    // 性能优化：预分配结果map的内存空间
    std::map<std::string, double> result;
    
    // First check if LPA is valid to avoid processing NO_LPA
    if (lpa == NO_LPA) {
        result["status"] = 0;  // Miss
        result["delay"] = 0;
        result["tre"] = 0;
        return result;
    }
    
    this->current_time = current_time;
    
    // 更新总IO计数和读统计信息
    total_io_count++; // 确保读请求也计入总IO计数，这样才能正确计算工作负载强度
    read_count++;
    total_read_count++;

    // Update read count for both RL agents (for delayed Q-table updates)
    if (agent_layer1 && agent_layer1->use_delayed_update) {
        agent_layer1->increment_read_count();
    }
    if (agent_layer2 && agent_layer2->use_delayed_update) {
        agent_layer2->increment_read_count();
    }
    
    // 性能优化：减少分类参数更新频率
    if (total_io_count % 50000 == 0) {
        // 更新动态分类参数
        update_classification_parameters();
    }
    
    // Check if page is mapped to a block
    auto block_it = page_to_block.find(lpa);
    
    // If page is not mapped, return miss
    if (block_it == page_to_block.end()) {
        read_miss_count++;
        result["status"] = 0;  // Miss
        result["delay"] = 0;
        result["tre"] = 0;
        return result;
    }
    
    // Get block data
    const flash_block_ID_type block_id = block_it->second;
    RLBlock* block = blocks[block_id];

    // 更新块的访问频率统计（用于联合评分）
    update_block_access_frequency(block_id, current_time);
    
    // 性能优化：仅在需要时获取平面ID和增加访问计数
    static const sim_time_type CLASSIFY_INTERVAL = classification_interval * 3600 * 1e9;
    
    auto lpa_to_ppa_it = lpa_to_ppa_mapping.find(lpa);
    if (lpa_to_ppa_it != lpa_to_ppa_mapping.end()) {
        PlaneID plane_id = get_plane_id(lpa_to_ppa_it->second);
        
        auto plane_it = plane_block_lists.find(plane_id);
        if (plane_it != plane_block_lists.end()) {
            // 增加平面访问计数
            plane_it->second.access_count++;
            
            // 性能优化：大幅减少读操作时的重新分类频率，提高读缓存性能
            // 检查是否需要重新分类该平面的块（读操作时降低频率）
            if ((plane_it->second.access_count >= 10000) ||
                (current_time - plane_it->second.last_classify_time > CLASSIFY_INTERVAL * 2)) {
                // 重置访问计数并更新最后分类时间
                plane_it->second.access_count = 0;
                plane_it->second.last_classify_time = current_time;

                // 对该平面的块进行分类（读操作时跳过以提高性能）
                classify_blocks_in_plane(plane_id);  // 注释掉以提高读缓存性能
            }
        }
    }
    
    // 性能优化：通过引用访问页面信息，避免复制
    RLPageInfo& info = page_info[lpa];
    
    // Record timestamp - 性能优化：预先分配容器空间
    if (info.access_timestamps.capacity() < info.access_timestamps.size() + 1) {
        info.access_timestamps.reserve(info.access_timestamps.size() * 2 + 8);
    }
    info.access_timestamps.push_back(current_time);
    
    // 性能优化：使用之前查找的迭代器而不是再次查找
    NVM::FlashMemory::Physical_Page_Address ppa;
    bool has_ppa = false;
    if (lpa_to_ppa_it != lpa_to_ppa_mapping.end()) {
        ppa = lpa_to_ppa_it->second;
        has_ppa = true;
    }
    
    // Initialize delay values
    double delay_us = 0.0;
    bool tre_triggered = false;
    
    // If a delay_us_rber value was provided, use it directly
    if (delay_us_rber >= 0) {
        delay_us = delay_us_rber;
    }

    // === 两层RL模型奖励计算和Q表更新 ===
    // 基于读请求的延时计算奖励，并更新两个RL代理的Q表
    // 这是RL学习的核心：通过读请求的性能反馈来优化写请求的决策

    // 获取TRE数据（如果有PPA映射且TRE模块可用）
    SSD_Components::TRE_Data tre_data;
    if (has_ppa && tre_module != nullptr) {
        tre_data = tre_module->Update_Page_Access(ppa, false);  // false表示读操作
    }

    update_two_layer_rl_rewards(lpa, delay_us, tre_data);

    // Set result values
    result["status"] = 1;  // Hit
    result["delay"] = delay_us;
    result["tre"] = tre_triggered ? 1.0 : 0.0;

    return result;
}

// Fast state index calculation using pre-computed multipliers (simplified 3D state)
inline int RLManager::fast_calculate_state_index_simplified(int long_term_count, int weighted_access_interval, int block_class) {
    if (!cache_simplified.initialized) {
        // Initialize cache if not already done
        cache_simplified.multipliers = {1, 3, 9};  // 3*3*3=27 (simplified dimensions)
        cache_simplified.initialized = true;
    }

    // Direct calculation without loops is faster
    // Simplified state vector: <long_term_count, weighted_access_interval, block_class>
    return long_term_count * cache_simplified.multipliers[0] +
           weighted_access_interval * cache_simplified.multipliers[1] +
           block_class * cache_simplified.multipliers[2];
}

// Fast state index calculation using pre-computed multipliers (original 5D state for compatibility)
inline int RLManager::fast_calculate_state_index(int long_term_count, int weighted_access_interval,
                                              int neighbor_access_state, int size_category, int block_class) {
    if (!cache.initialized) {
        // Initialize cache if not already done
        cache.multipliers = {1, 3, 9, 18, 36};  // 3*3*2*2*3=108 (new dimensions)
        cache.initialized = true;
    }

    // Direct calculation without loops is faster
    // New state vector: <long_term_count, weighted_access_interval, size_category, block_class>
    return long_term_count * cache.multipliers[0] +
           weighted_access_interval * cache.multipliers[1] +
           neighbor_access_state * cache.multipliers[2] +
           size_category * cache.multipliers[3] +
           block_class * cache.multipliers[4];
}


// Get a block for writing from the specified block class and page type
// This implements the 9-list strategy:
// - First try to get a block from the specified class and page type list
// - If empty, try other page type lists in the same block class (prefer longer lists)
// - If still no blocks, try to get a block from the stateless pool
RLBlock* RLManager::get_block_for_write(BlockClass block_class, PageType page_type) {
    // Convert enums to indices
    int class_idx = static_cast<int>(block_class);
    int page_type_idx = static_cast<int>(page_type);
    
    // Check if we have blocks of the desired type
    auto& target_list = block_lists[class_idx][page_type_idx];
    
    if (!target_list.blocks.empty()) {
        // Get the head of the list (LRU block)
        RLBlock* block = target_list.blocks.front();
        
        // Remove from the list
        target_list.blocks.pop_front();
        
        return block;
    }
    
    // If no blocks of the desired page type, try other page types in the same block class
    // Find lengths of the other two page type lists in this block class
    std::vector<std::pair<int, size_t>> other_lists;
    for (int pt_idx = 0; pt_idx < NUM_PAGE_TYPES; pt_idx++) {
        if (pt_idx != page_type_idx) {
            other_lists.push_back({pt_idx, block_lists[class_idx][pt_idx].blocks.size()});
        }
    }
    
    // Sort by list length, prioritizing longer lists
    std::sort(other_lists.begin(), other_lists.end(), 
              [](const std::pair<int, size_t>& a, const std::pair<int, size_t>& b) {
                  return a.second > b.second;
              });
    
    // 按照排序后的顺序尝试获取块
    for (const auto& list_info : other_lists) {
        int pt_idx = list_info.first;
        if (!block_lists[class_idx][pt_idx].blocks.empty()) {
            // Get from another page type list
            RLBlock* block = block_lists[class_idx][pt_idx].blocks.front();
            block_lists[class_idx][pt_idx].blocks.pop_front();
            
            // Log mismatch
            page_type_mismatch_count++;
            
            return block;
        }
    }
    
    // If no blocks available in this class, try to get one from stateless pool
    if (!stateless_blocks.empty()) {
        // Get a block from stateless pool
        RLBlock* block = stateless_blocks.back();
        stateless_blocks.pop_back();
        
        // Set its class
        block->block_class = block_class;
        
        return block;
    }
    
    // No blocks available
    return nullptr;
}

// === 9个链表块移动核心函数 ===
// 功能：将块从当前页面类型链表移动到下一个页面类型链表
//
// 9个链表结构：
// - HOT_LP, HOT_MP, HOT_UP (热块的3个页面类型链表)
// - WARM_LP, WARM_MP, WARM_UP (暖块的3个页面类型链表)
// - COLD_LP, COLD_MP, COLD_UP (冷块的3个页面类型链表)
//
// 块移动规则：
// 1. 写入操作后，块的下一个可写页面类型发生变化：LP → MP → UP → LP (循环)
// 2. 块需要从当前页面类型链表移除，并加入到下一个页面类型链表的尾部
// 3. 如果块已满，移动到full_blocks池，并从stateless池中选择PEC最少的块补充
// 4. 块的温度类别(HOT/WARM/COLD)在此过程中保持不变，只改变页面类型
//
// 参数说明：
// - block: 要移动的块指针
// - block_class: 块的温度类别(HOT/WARM/COLD)，决定目标链表的类别维度
// - current_page_type: 当前页面类型(LP/MP/UP)，用于计算下一个页面类型
void RLManager::move_block_to_next_page_type(RLBlock* block, BlockClass block_class, PageType current_page_type) {
    // 注意：块已经在get_block_for_write函数中从原链表移除
    // 这里只需要将块添加到下一个合适的链表中
    
    // Calculate the next page type - blocks cycle through LP → MP → UP → LP...
    PageType next_page_type;
    
    switch (current_page_type) {
        case PageType::LP:
            next_page_type = PageType::MP;
            break;
        case PageType::MP:
            next_page_type = PageType::UP;
            break;
        case PageType::UP:
            next_page_type = PageType::LP;
            break;
        default:
            next_page_type = PageType::LP; // Should never happen
    }
    
    // === 块已满的处理逻辑 ===
    if (block->free_pages == 0) {
        // 1. 将已满的块移动到full_blocks池
        // 已满的块不再参与写入操作，从9个活跃链表中完全移除
        full_blocks.push_back(block);

        // 2. 检查当前温度类别的块数量是否需要补充
        int class_idx = static_cast<int>(block_class);
        unsigned int total_blocks_in_class = 0;

        // 统计当前温度类别在所有3个页面类型链表中的总块数
        for (int page_type_idx = 0; page_type_idx < NUM_PAGE_TYPES; page_type_idx++) {
            total_blocks_in_class += block_lists[class_idx][page_type_idx].blocks.size();
        }

        // 3. 如果该温度类别的活跃块数量不足，从无状态池补充
        // 目标：每个温度类别维持足够的活跃块数量以保证写入性能
        if (total_blocks_in_class < 4) {  // 阈值：每个温度类别最少8个活跃块

            // 4. 从无状态池中选择PEC最少的块作为替代
            // 优先选择磨损程度最低的块，延长SSD寿命
            if (!stateless_blocks.empty()) {
                RLBlock* min_pec_block = nullptr;
                unsigned int min_pec = std::numeric_limits<unsigned int>::max();

                // 遍历无状态池，找到PEC最小的块
                for (auto& stateless_block : stateless_blocks) {
                    if (stateless_block->pec < min_pec) {
                        min_pec = stateless_block->pec;
                        min_pec_block = stateless_block;
                    }
                }

                if (min_pec_block != nullptr) {
                    // 5. 执行块的链表移动操作
                    // a) 从无状态池中移除选中的块
                    auto stateless_it = std::find(stateless_blocks.begin(), stateless_blocks.end(), min_pec_block);
                    if (stateless_it != stateless_blocks.end()) {
                        stateless_blocks.erase(stateless_it);
                    }

                    // b) 更新块的温度类别属性
                    min_pec_block->block_class = block_class;

                    // c) 将新块添加到对应温度类别的LP链表尾部
                    // 新块总是从LP页面类型开始，因为它是全新的未写入块
                    block_lists[class_idx][0].blocks.push_back(min_pec_block);

                    // Block movement completed (logging removed)
                }
            }
        }

        return;  // 块已满，处理完毕，不需要继续执行下面的页面类型移动逻辑
    }

    // === 正常的页面类型移动逻辑 ===
    // 块未满，需要移动到下一个页面类型链表

    // 1. 计算目标链表的索引
    int class_idx = static_cast<int>(block_class);      // 温度类别索引 (0=HOT, 1=WARM, 2=COLD)
    int next_type_idx = static_cast<int>(next_page_type); // 下一个页面类型索引 (0=LP, 1=MP, 2=UP)

    // 2. 将块添加到目标链表的尾部
    // 添加到尾部表示这是最近使用的块，符合LRU策略
    // 目标链表：block_lists[温度类别][页面类型]
    block_lists[class_idx][next_type_idx].blocks.push_back(block);

    // 3. 块移动完成
    // 此时块已经：
    // - 从原链表 block_lists[class_idx][current_page_type] 中移除（在get_block_for_write中完成）
    // - 添加到新链表 block_lists[class_idx][next_page_type] 的尾部（在此处完成）
    // - 块的温度类别保持不变，只改变了页面类型维度
}

// 已移除add_new_block_to_pool函数，功能已整合到move_block_to_next_page_type函数中

// 已移除balance_block_lists函数，现在在块满时直接从无状态池选择PEC最少的块添加到对应温度类型的LP链表



// 初始化平面级块池管理
void RLManager::initialize_plane_block_lists(unsigned int channel_count, unsigned int chip_count, 
                                            unsigned int die_count, unsigned int plane_count) {
    // 清空现有的平面块列表
    plane_block_lists.clear();
    
    // 创建每个平面的块列表
    for (unsigned int channel = 0; channel < channel_count; channel++) {
        for (unsigned int chip = 0; chip < chip_count; chip++) {
            for (unsigned int die = 0; die < die_count; die++) {
                for (unsigned int plane = 0; plane < plane_count; plane++) {
                    // 创建平面ID
                    PlaneID plane_id;
                    plane_id.channel_id = channel;
                    plane_id.chip_id = chip;
                    plane_id.die_id = die;
                    plane_id.plane_id = plane;
                    
                    // 初始化此平面的块列表
                    PlaneBlockList& plane_list = plane_block_lists[plane_id];
                    
                    // 为每个块类别和页面类型设置目标块数，使用XML配置中的target_blocks_per_class参数
                    unsigned int blocks_per_class = target_blocks_per_class; // 使用XML中配置的值
                    //unsigned int blocks_per_page_type = blocks_per_class / NUM_PAGE_TYPES;
                    
                    for (int class_idx = 0; class_idx < NUM_BLOCK_CLASSES; class_idx++) {
                        for (int page_type_idx = 0; page_type_idx < NUM_PAGE_TYPES; page_type_idx++) {
                            // 在页面类型之间分配，保持总数为blocks_per_class
                            if (page_type_idx == 0) { // LP
                                plane_list.block_lists[class_idx][page_type_idx].target_count = 10;
                            } else if (page_type_idx == 1) { // MP
                                plane_list.block_lists[class_idx][page_type_idx].target_count = 0;
                            } else { // UP
                                plane_list.block_lists[class_idx][page_type_idx].target_count = 0;
                            }
                            // 确保每种类型至少有2个块
                            //plane_list.block_lists[class_idx][page_type_idx].target_count = std::max(2u, plane_list.block_lists[class_idx][page_type_idx].target_count);
                        }
                    }
                }
            }
        }
    }
    
    // 为每个块设置平面ID - 修改为从0开始的连续编码分配方式
    unsigned int total_planes = channel_count * chip_count * die_count * plane_count;
    unsigned int blocks_per_plane = blocks.size() / total_planes;

    for (auto block : blocks) {
        // 使用从0开始的连续编码方式计算平面ID
        unsigned int block_id = block->block_id;

        // 计算该块属于哪个平面（从0开始连续编码）
        unsigned int plane_idx = block_id / blocks_per_plane;

        // 确保plane_idx不超出范围
        if (plane_idx >= total_planes) {
            plane_idx = total_planes - 1;
        }

        // 按照 Channel -> Chip -> Die -> Plane 的层次结构顺序计算各级别ID
        unsigned int channel = plane_idx / (chip_count * die_count * plane_count);
        unsigned int remaining = plane_idx % (chip_count * die_count * plane_count);
        unsigned int chip = remaining / (die_count * plane_count);
        remaining = remaining % (die_count * plane_count);
        unsigned int die = remaining / plane_count;
        unsigned int plane = remaining % plane_count;

        // 设置块的平面ID
        block->channel_id = channel;
        block->chip_id = chip;
        block->die_id = die;
        block->plane_id = plane;
    }
    
    // Plane-level block management initialized (logging removed)
}

// 获取平面ID
PlaneID RLManager::get_plane_id(const NVM::FlashMemory::Physical_Page_Address& address) {
    PlaneID plane_id;

    // 设置完整的层次结构标识，确保每个平面都有唯一的标识符
    // 按照正确的层次结构顺序: Channel->Chip->Die->Plane
    plane_id.channel_id = address.ChannelID;
    plane_id.chip_id = address.ChipID;
    plane_id.die_id = address.DieID;
    plane_id.plane_id = address.PlaneID;

    return plane_id;
}



// 在指定平面中获取块用于写入 - 使用新的块管理逻辑
RLBlock* RLManager::get_block_for_write_in_plane(const PlaneID& plane_id, BlockClass block_class, PageType page_type, stream_id_type stream_id) {
    // 使用新的RL动作执行逻辑
    RLBlock* selected_block = execute_rl_action(plane_id, block_class, page_type);

    if (selected_block == nullptr) {
        return nullptr; // 无法分配块
    }

    // 设置块的stream_id
    selected_block->stream_id = stream_id;

    return selected_block;
}

// 获取所有平面ID的辅助函数
std::vector<PlaneID> RLManager::get_all_plane_ids(unsigned int channel_count, unsigned int chip_count, 
                                                  unsigned int die_count, unsigned int plane_count) {
    std::vector<PlaneID> plane_ids;
    
    // 为所有平面创建ID，确保按照Channel -> Chip -> Die -> Plane的顺序枚举
    for (unsigned int channel = 0; channel < channel_count; channel++) {
        for (unsigned int chip = 0; chip < chip_count; chip++) {
            for (unsigned int die = 0; die < die_count; die++) {
                for (unsigned int plane = 0; plane < plane_count; plane++) {
                    PlaneID plane_id;
                    plane_id.channel_id = channel;
                    plane_id.chip_id = chip;
                    plane_id.die_id = die;
                    plane_id.plane_id = plane;
                    plane_ids.push_back(plane_id);
                }
            }
        }
    }
    
    return plane_ids;
}

// 对平面内的块进行分类，保持各类型块数量固定
void RLManager::classify_blocks_in_plane(const PlaneID& plane_id) {
    if (plane_block_lists.find(plane_id) == plane_block_lists.end()) {
        return;  // Plane not found
    }
    
    PlaneBlockList& plane_list = plane_block_lists[plane_id];
    
    // 获取配置中每个类型应维持的块数量 (使用从XML读取的target_blocks_per_class参数)
    unsigned int blocks_per_class = target_blocks_per_class; // 从XML配置中读取，而非硬编码值
    
    // 用于存储需要重新分类的块
    std::vector<std::pair<double, RLBlock*>> scored_blocks;
    
    // 记录每个块的原始类别和热度得分
    std::map<RLBlock*, std::pair<BlockClass, double>> block_scores;
    
    // 用于临时存储保持原类别的块（按原顺序）
    std::map<int, std::map<int, std::list<RLBlock*>>> unchanged_blocks;
    
    // 遍历每个块类型和页面类型的链表，计算热度得分
    for (int class_idx = 0; class_idx < NUM_BLOCK_CLASSES; class_idx++) {
        for (int page_type_idx = 0; page_type_idx < NUM_PAGE_TYPES; page_type_idx++) {
            auto& list = plane_list.block_lists[class_idx][page_type_idx];
            
            // 将链表中的块收集并计算得分
            for (auto block : list.blocks) {
                // 跳过已满块
                if (block->free_pages == 0) continue;
                
                // 记录块的原始类别
                BlockClass original_class = block->block_class;
                
                // 计算块的联合分数（访问频率 + idle时间）
                double score = calculate_joint_score(block);
                
                // 存储块的得分和原始类别信息
                block_scores[block] = std::make_pair(original_class, score);
                
                // 添加到得分列表中用于后续排序
                scored_blocks.push_back({score, block});
            }
            
            // 清空原链表（稍后会重新填充）
            list.blocks.clear();
        }
    }
    
    // 根据分数从高到低排序块
    std::sort(scored_blocks.begin(), scored_blocks.end(),
              [](const auto& a, const auto& b) { return a.first > b.first; });

    // === 新的按分数区间分类策略 ===
    // 改成按照分数而不是比例进行分类（最大分数和最小分数区间内在前百分30为热块，中间30为暖块，后40为冷块）

    size_t total_blocks = scored_blocks.size();
    std::map<RLBlock*, BlockClass> new_class_map;

    if (total_blocks > 0) {
        // 获取最大分数和最小分数
        double max_score = scored_blocks[0].first;  // 已排序，第一个是最大分数
        double min_score = scored_blocks[total_blocks - 1].first;  // 最后一个是最小分数
        double score_range = max_score - min_score;


        // 联合评分分类：两个指标均满足条件才能分类为热块或冷块
        // 计算每个块的访问频率得分和idle时间得分
        std::vector<std::pair<double, RLBlock*>> frequency_scores;
        std::vector<std::pair<double, RLBlock*>> idle_scores;

        for (size_t i = 0; i < total_blocks; i++) {
            RLBlock* block = scored_blocks[i].second;
            double frequency_score = calculate_access_frequency_score(block);
            double idle_score = calculate_block_temperature_score(block);

            frequency_scores.push_back({frequency_score, block});
            idle_scores.push_back({idle_score, block});
        }

        // 分别对两个指标排序
        std::sort(frequency_scores.begin(), frequency_scores.end(),
                  [](const auto& a, const auto& b) { return a.first > b.first; });
        std::sort(idle_scores.begin(), idle_scores.end(),
                  [](const auto& a, const auto& b) { return a.first > b.first; });

        // 计算阈值位置
        size_t hot_threshold_pos = static_cast<size_t>(total_blocks * 0.3);   // 前30%
        size_t cold_threshold_pos = static_cast<size_t>(total_blocks * 0.7);  // 后30%（从70%开始）

        // 获取阈值分数
        double frequency_hot_threshold = frequency_scores[hot_threshold_pos].first;
        double frequency_cold_threshold = frequency_scores[cold_threshold_pos].first;
        double idle_hot_threshold = idle_scores[hot_threshold_pos].first;
        double idle_cold_threshold = idle_scores[cold_threshold_pos].first;

        // 分类：两个指标均满足条件
        for (size_t i = 0; i < total_blocks; i++) {
            RLBlock* block = scored_blocks[i].second;
            double frequency_score = calculate_access_frequency_score(block);
            double idle_score = calculate_block_temperature_score(block);

            // 热块：两个指标都在前30%
            if (frequency_score >= frequency_hot_threshold && idle_score >= idle_hot_threshold) {
                new_class_map[block] = BlockClass::HOT;
            }
            // 冷块：两个指标都在后30%
            else if (frequency_score <= frequency_cold_threshold && idle_score <= idle_cold_threshold) {
                new_class_map[block] = BlockClass::COLD;
            }
            // 暖块：其余情况
            else {
                new_class_map[block] = BlockClass::WARM;
            }
        }
    }
    
    // === 9个链表重新分类后的块移动处理 ===
    // 根据新的温度分类结果，将块分为两组进行不同的处理

    std::vector<RLBlock*> changing_blocks;  // 需要改变温度类别的块

    for (auto& pair : block_scores) {
        RLBlock* block = pair.first;
        BlockClass original_class = pair.second.first;   // 原始温度类别
        BlockClass new_class = new_class_map[block];     // 新的温度类别

        // 确定当前块的页面类型（LP/MP/UP）
        // 页面类型在重新分类过程中保持不变，只有温度类别可能改变
        PageType current_page_type = determine_block_current_page_type(block);
        int page_type_idx = static_cast<int>(current_page_type);

        if (original_class == new_class) {
            // === 情况1：温度类别未改变 ===
            // 块保持在相同的温度类别链表中，维持原有的访问顺序
            // 这样可以保持块的时间局部性，避免不必要的链表重排
            unchanged_blocks[static_cast<int>(new_class)][page_type_idx].push_back(block);
        } else {
            // === 情况2：温度类别发生改变 ===
            // 块需要从原温度类别的链表移动到新温度类别的链表
            // 例如：从HOT_LP移动到WARM_LP，或从COLD_MP移动到HOT_MP

            // 1. 更新块的温度类别属性
            block->block_class = new_class;

            // 2. 记录到需要移动的列表中，稍后统一处理
            changing_blocks.push_back(block);

            // 注意：块已经在前面的代码中从原链表移除（list.blocks.clear()）
            // 这里只需要确定新的目标链表位置
        }
    }
    
    // === 第一阶段：重新填充温度类别不变的块 ===
    // 优先处理温度类别未改变的块，保持它们的原有访问顺序
    // 这样可以维持块的时间局部性，提高缓存效率

    for (int class_idx = 0; class_idx < NUM_BLOCK_CLASSES; class_idx++) {
        for (int page_type_idx = 0; page_type_idx < NUM_PAGE_TYPES; page_type_idx++) {

            // 检查是否有温度类别不变的块需要添加到当前链表
            if (unchanged_blocks.find(class_idx) != unchanged_blocks.end() &&
                unchanged_blocks[class_idx].find(page_type_idx) != unchanged_blocks[class_idx].end()) {

                auto& unchanged_list = unchanged_blocks[class_idx][page_type_idx];
                auto& target_list = plane_list.block_lists[class_idx][page_type_idx].blocks;

                // 按原有顺序将块重新添加到链表中
                // 这保持了块的原始访问模式和时间局部性
                for (auto block : unchanged_list) {
                    target_list.push_back(block);
                }
            }
        }
    }

    // === 第二阶段：处理温度类别发生改变的块 ===
    // 将改变温度类别的块添加到新的目标链表尾部
    // 添加到尾部表示这些块是"新加入"的，符合LRU策略

    for (auto block : changing_blocks) {
        // 1. 获取块的新温度类别和当前页面类型
        int class_idx = static_cast<int>(block->block_class);  // 新的温度类别索引
        PageType current_page_type = determine_block_current_page_type(block);
        int page_type_idx = static_cast<int>(current_page_type);  // 页面类型保持不变

        // 2. 执行链表移动：从旧链表到新链表
        // 旧链表：block_lists[原温度类别][页面类型] (已在前面清空)
        // 新链表：block_lists[新温度类别][页面类型] (在此处添加)
        plane_list.block_lists[class_idx][page_type_idx].blocks.push_back(block);

        // 3. 块移动完成
        // 此时块已经：
        // - 更新了温度类别属性 (block->block_class = new_class)
        // - 从原温度类别链表中移除 (通过list.blocks.clear()完成)
        // - 添加到新温度类别链表的尾部 (在此处完成)
        // - 页面类型维度保持不变
    }
    
    // 记录分类后每种类型块的数量
    if (enable_classification_logging) {
        unsigned int hot_count = plane_list.block_lists[static_cast<int>(BlockClass::HOT)][0].blocks.size() + 
                               plane_list.block_lists[static_cast<int>(BlockClass::HOT)][1].blocks.size() + 
                               plane_list.block_lists[static_cast<int>(BlockClass::HOT)][2].blocks.size();
                               
        unsigned int warm_count = plane_list.block_lists[static_cast<int>(BlockClass::WARM)][0].blocks.size() + 
                                plane_list.block_lists[static_cast<int>(BlockClass::WARM)][1].blocks.size() + 
                                plane_list.block_lists[static_cast<int>(BlockClass::WARM)][2].blocks.size();
                                
        unsigned int cold_count = plane_list.block_lists[static_cast<int>(BlockClass::COLD)][0].blocks.size() + 
                                plane_list.block_lists[static_cast<int>(BlockClass::COLD)][1].blocks.size() + 
                                plane_list.block_lists[static_cast<int>(BlockClass::COLD)][2].blocks.size();
        
        // Plane classification results (logging removed)
    }
}

// 确定块当前的页面类型
PageType RLManager::determine_block_current_page_type(RLBlock* block) {
    if (block == nullptr) {
        return PageType::LP; // 默认为LP
    }
    
    // 基于已写入页面数和页面循环模式(LP->MP->UP->LP)确定当前页面类型
    unsigned int written_pages = block->next_write_page_index % pages_per_block;
    unsigned int cycle_position = written_pages % 3;
    
    switch (cycle_position) {
        case 0: return PageType::LP;
        case 1: return PageType::MP;
        case 2: return PageType::UP;
        default: return PageType::LP;
    }
}

bool RLManager::allocate_page_based_on_rl(LPA_type lpa, sim_time_type current_time, unsigned int num_pages, NVM::FlashMemory::Physical_Page_Address& address, stream_id_type stream_id) {
    // 首先检查LPA是否有效，避免处理NO_LPA
    if (lpa == NO_LPA) {
        return false;
    }
    
    // 更新当前时间和总IO计数
    this->current_time = current_time;
    total_io_count++;
    
    // 每50000个IO操作检查一次是否需要更新分类参数
    // 确保阈值动态调整能够真正生效
    if (total_io_count % 50000 == 0) {
        // 如果使用动态状态分类，先计算当前阈值下的状态
        if (use_dynamic_state_classification) {
            // 保存更新前的阈值
            unsigned int old_access_count_threshold = state_thresholds.dynamic_access_count_threshold;
            sim_time_type old_access_interval_threshold = state_thresholds.dynamic_access_interval_threshold;
            
            // 统计有多少LPA的状态会因为阈值更新而变化
            unsigned int changed_states = 0;
            unsigned int checked_states = 0;
            
            // 优化：减少采样数量，从1000个减少到100个
            const unsigned int sample_size = 100;
            std::vector<LPA_type> active_lpas;
            
            // 优化：限制循环遍历次数，避免在大型工作负载中过度搜索
            unsigned int iterations = 0;
            const unsigned int max_iterations = 10000;
            
            // 收集活跃的LPA
            for (const auto& entry : page_info) {
                if (entry.second.access_timestamps.size() >= 2) {
                    active_lpas.push_back(entry.first);
                    if (active_lpas.size() >= sample_size) break;
                }
                
                // 避免在大型工作负载中过度循环
                iterations++;
                if (iterations >= max_iterations) break;
            }
            
            // 计算这些LPA在当前阈值下的状态
            std::unordered_map<LPA_type, int> current_states;
            for (const auto& lpa : active_lpas) {
                current_states[lpa] = discretize_state(lpa, current_time);
                checked_states++;
            }
            
            // 更新动态分类参数
            update_classification_parameters();
            
            // 计算这些LPA在新阈值下的状态，并统计变化的数量
            for (const auto& lpa : active_lpas) {
                int new_state = discretize_state(lpa, current_time);
                if (new_state != current_states[lpa]) {
                    changed_states++;
                }
            }

        } 
        
        // Dynamic parameter check completed (logging removed)
    }
    

    // 获取或创建页面信息
    RLPageInfo& info = page_info[lpa];
    info.access_timestamps.push_back(current_time);
    info.num_pages = std::max(info.num_pages, num_pages);

    // 获取平面ID
    PlaneID plane_id = get_plane_id(address);
    
    // === 两层RL模型决策过程 ===

    // Layer 1 RL: 预测块类别
    // 使用原有的5维状态向量进行块类别预测
    int state_layer1 = discretize_state(lpa, current_time);

    // 保存Layer 1写入前的状态 S
    info.last_state = state_layer1;

    // 更新Layer 1页面访问历史
    if (info.last_state_index >= 0) {
        // 记录Layer 1状态转换
        state_transition_pairs[{info.last_state_index, state_layer1}]++;
        write_state_action_count_layer1[{info.last_state_index, state_layer1}]++;
    }
    info.last_state_index = state_layer1;

    // Layer 1: 获取可用块类别的mask（mask掉没有可供写入的块类型的Q表值）
    std::vector<bool> block_class_mask = get_available_block_classes_mask(plane_id);

    // === RL动作选择完整执行逻辑 ===
    // 1. 先mask不可用的动作
    // 2. 根据概率决定两个RL模型是随机还是贪婪决策
    // 3. 如果其中有一个RL是随机探索：各自按照响应策略进行动作选择
    // 4. 如果两个RL都不是随机探索：进入联合决策分支

    // 验证Layer 1掩码有效性
    bool has_valid_block_actions = false;
    for (bool mask : block_class_mask) {
        if (mask) { has_valid_block_actions = true; break; }
    }

    std::pair<int, int> joint_decision;

    // 如果Layer 1没有有效动作，使用默认动作
    if (!has_valid_block_actions) {
        joint_decision = std::make_pair(0, 0);  // 默认动作
    } else {
        // === 根据概率决定两个RL模型是随机还是贪婪决策 ===
        bool should_explore_layer1 = agent_layer1->should_explore();
        bool should_explore_layer2 = agent_layer2->should_explore();

        if (should_explore_layer1 || should_explore_layer2) {
            // === 有一个RL是随机探索：各自按照响应策略进行动作选择 ===

            int block_class_action;
            if (should_explore_layer1) {
                // 第一层随机选择
                block_class_action = agent_layer1->select_random_action_with_mask(state_layer1, block_class_mask);
            } else {
                // 第一层贪婪选择
                block_class_action = agent_layer1->select_greedy_action_with_mask(state_layer1, block_class_mask);
            }

            // 基于第一层决策重新计算第二层状态和掩码
            BlockClass selected_block_class = static_cast<BlockClass>(block_class_action);
            int updated_state_layer2 = calculate_state_index_layer2(selected_block_class, plane_id);
            std::vector<bool> updated_mask_layer2 = get_available_page_types_mask(plane_id, selected_block_class);

            int page_type_action;
            if (should_explore_layer2) {
                // 第二层随机选择（基于第一层决策的块类型状态）
                page_type_action = agent_layer2->select_random_action_with_mask(updated_state_layer2, updated_mask_layer2);
            } else {
                // 第二层贪婪选择
                page_type_action = agent_layer2->select_greedy_action_with_mask(updated_state_layer2, updated_mask_layer2);
            }

            joint_decision = std::make_pair(block_class_action, page_type_action);
        } else {
            // === 两个RL都不是随机探索：进入联合决策分支 ===
            // 联合决策函数内部会为每个块类型动态计算正确的页面类型mask
            joint_decision = make_joint_decision(state_layer1, block_class_mask, plane_id);
        }
    }

    int block_class_action = joint_decision.first;
    int page_type_action = joint_decision.second;

    BlockClass selected_block_class = static_cast<BlockClass>(block_class_action);
    PageType predicted_page_type = static_cast<PageType>(page_type_action);

    // 重新计算Layer 2状态（基于实际选择的块类别）
    int state_layer2 = calculate_state_index_layer2(selected_block_class, plane_id);

    // 重新获取Layer 2的精确mask（基于实际选择的块类别）
    std::vector<bool> page_type_mask = get_available_page_types_mask(plane_id, selected_block_class);

    // 跟踪Layer 1动作执行计数
    write_action_count_layer1[block_class_action]++;
    write_state_count_layer1[state_layer1]++;

    // 跟踪Layer 2动作执行计数
    write_action_count_layer2[page_type_action]++;
    write_state_count_layer2[state_layer2]++;

    // 组合动作用于兼容性统计（Layer1动作 * 3 + Layer2动作）
    int combined_action = block_class_action * 3 + page_type_action;
    action_execution_count[combined_action]++;
    
    // 更新页面信息中的最后选择页面类型
    info.page_type = predicted_page_type;
    info.last_block_class = selected_block_class;
    
    // 在当前平面中获取块用于写入，传递stream_id
    RLBlock* selected_block = get_block_for_write_in_plane(plane_id, selected_block_class, predicted_page_type, stream_id);
    
    // 如果找不到合适的块，报告写入失败
    if (!selected_block) {
        write_miss_count++;
        return false;
    }
    
    // 获取选定块的实际页面类型
    PageType actual_page_type = last_actual_page_type;
    
    // 尝试写入页面
    bool write_success = selected_block->write_page(lpa, current_time, true);
    
    // 如果写入成功
    if (write_success) {
        // 更新页面到块的映射
        page_to_block[lpa] = selected_block->block_id;
        
        // 更新LPA到PPA的映射
        address.ChannelID = selected_block->channel_id;
        address.ChipID = selected_block->chip_id;
        address.DieID = selected_block->die_id;
        address.PlaneID = selected_block->plane_id;
        
        // 计算平面内的本地块ID，使用块的新方法
        // 全局块ID需要转换为平面内的本地块ID
        unsigned int channel_count = mqsim_block_manager->Get_channel_count();
        unsigned int chip_count = mqsim_block_manager->Get_chip_no_per_channel();
        unsigned int die_count = mqsim_block_manager->Get_die_no_per_chip();
        unsigned int plane_count = mqsim_block_manager->Get_plane_no_per_die();
        
        // 计算平面内的本地块ID
        unsigned int total_planes = channel_count * chip_count * die_count * plane_count;
        unsigned int blocks_per_plane = blocks.size() / total_planes;
        
        // 使用块的get_local_block_id方法获取本地块ID
        unsigned int local_block_id = selected_block->get_local_block_id(blocks_per_plane);
        
        // Block allocation completed (logging removed)

        address.BlockID = local_block_id;
        address.PageID = selected_block->next_write_page_index - 1; // 刚写入的页面
        //std::cout<<"address.BlockID: "<<address.BlockID<<std::endl;
        //std::cout<<"address.PageID: "<<address.PageID<<std::endl;
        //std::cout << "clb_RL_Management " << address.ChannelID << " " << address.ChipID << " " << address.DieID << " " << address.PlaneID << " " << address.BlockID << " " << address.PageID << std::endl;

        // 存储映射
        lpa_to_ppa_mapping[lpa] = address;
        
        // 增加写入计数
        write_count++;

        // 更新块的访问频率统计（用于联合评分）
        update_block_access_frequency(selected_block->block_id, current_time);

        // === 新的块管理逻辑：处理块的移动和替换 ===

        // 检查块是否写满
        if (is_block_full(selected_block)) {
            // 块写满后，移出对应类型块池，放入已满块池，并从未写块池中拿一个新块
            handle_full_block_in_plane(plane_id, selected_block, selected_block_class);
        } else {
            // 块未写满，移动到下一个页面类型的链表尾部
            move_block_to_next_page_type_in_plane(plane_id, selected_block, selected_block_class, actual_page_type);
        }


        // 增加当前平面的访问计数
        PlaneBlockList& plane_list = plane_block_lists[plane_id];
        plane_list.access_count++;
        
        // 检查是否需要重新分类该平面的块（每个平面被访问10000次IO时触发）
        // 使用平面级别的访问计数，每个平面独立触发重新分类
        if (plane_list.access_count >= 10000) {
            // 重置访问计数并更新最后分类时间
            plane_list.access_count = 0;
            plane_list.last_classify_time = current_time;

            // 对该平面的块进行分类（使用idle时间算分）
            classify_blocks_in_plane(plane_id);
        }
        
        // === 两层RL模型状态更新和保存 ===

        // Layer 1: 写入后重新计算状态 S'
        int next_state_layer1 = discretize_state(lpa, current_time);

        // Layer 2: 写入后重新计算状态 S'（基于实际使用的块类别和平均idle时间）
        int next_state_layer2 = calculate_state_index_layer2(selected_block_class, plane_id);

        // 保存Layer 1的S-A-S'信息用于后续奖励计算
        // S: state_layer1 (写入前状态)
        // A: block_class_action (预测的块类别动作)
        // S': next_state_layer1 (写入后状态)
        info.last_state_layer1 = state_layer1;
        info.last_action_layer1 = block_class_action;
        info.next_state_layer1 = next_state_layer1;

        // 保存Layer 2的S-A-S'信息用于后续奖励计算
        // S: state_layer2 (基于预测块类别的状态)
        // A: page_type_action (预测的页面类型动作)
        // S': next_state_layer2 (写入后基于实际块类别的状态)
        info.last_state_layer2 = state_layer2;
        info.last_action_layer2 = page_type_action;
        info.next_state_layer2 = next_state_layer2;

        // 兼容性：保存组合的动作和状态信息
        info.last_action = static_cast<int>(selected_block_class) * 3 + static_cast<int>(actual_page_type);
        info.next_state = next_state_layer1;  // 使用Layer 1的状态作为兼容性状态
        
        // 记录块ID和访问时间，用于后续读取时计算奖励
        info.last_block_id = selected_block->block_id;
        info.last_write_time = current_time;
        info.last_block_access_time = selected_block->last_access_time;
        
        // 更新 Flash_Block_Manager 中相应块的 Stream_id
        // 获取块的物理地址
        NVM::FlashMemory::Physical_Page_Address block_address;
        block_address.ChannelID = address.ChannelID;
        block_address.ChipID = address.ChipID;
        block_address.DieID = address.DieID;
        block_address.PlaneID = address.PlaneID;
        block_address.BlockID = address.BlockID;
        block_address.PageID = 0; // 页面ID不重要，我们只需要块的地址
        
        // 获取块的记录
        PlaneBookKeepingType* plane_record = mqsim_block_manager->Get_plane_bookkeeping_entry(block_address);
        
        // 更新块的 Stream_id
        if (plane_record != nullptr) {
            plane_record->Blocks[block_address.BlockID].Stream_id = stream_id;
        }
        
        // 更新块的访问时间
        //RLBlock* block = blocks[address.BlockID];
        //block->last_access_time = current_time;

        return true;
    } else {
        write_miss_count++;
        return false;
    }
}


// 解码状态索引为状态向量
std::tuple<int, int, int, int, int> RLManager::decode_state_index(int state_index) {
    // 基于维度大小: {3, 3, 2, 2, 3} = 108 states
    // 使用除法和取模运算逆向计算各维度值
    int long_term_count = state_index % 3;
    state_index /= 3;

    int weighted_access_interval = state_index % 3;
    state_index /= 3;

    int neighbor_access_state = state_index % 2;
    state_index /= 2;

    int size_category = state_index % 2;
    state_index /= 2;

    int block_class_value = state_index % 3;

    return std::make_tuple(long_term_count, weighted_access_interval,
                          neighbor_access_state, size_category, block_class_value);
}

// 两层RL模型的奖励计算和Q表更新
// 在读请求时调用，基于延时计算奖励并更新两个RL代理的Q表
void RLManager::update_two_layer_rl_rewards(LPA_type lpa, double delay_us, const SSD_Components::TRE_Data& tre_data) {
    auto info_it = page_info.find(lpa);
    if (info_it == page_info.end()) {
        return;  // 如果没有页面信息，跳过更新
    }

    RLPageInfo& info = info_it->second;

    // 检查是否有有效的状态和动作信息
    if (info.last_state_layer1 == -1 || info.last_action_layer1 == -1 ||
        info.last_state_layer2 == -1 || info.last_action_layer2 == -1) {
        return;  // 如果没有有效的状态动作信息，跳过更新
    }

    // 基于延时计算奖励
    // 奖励计算公式：延时越低，奖励越高
    // 延时范围通常在 [85, 1099] 微秒之间
    // 将延时映射到奖励范围 [3, -7]
    double reward = 0.0;
    if (delay_us <= 85) {
        // No delay, give highest reward
        reward = 3.0;
    } else if (delay_us <= 85+109) {
        reward = -1.0;
        // Low delay, map to [3, 0] range
        //reward = round(3.0 - (delay_us / 85.0) * 3.0);
    } else if (delay_us <= 194+133) {
        reward = -2.0;
    } else if (delay_us <= 327+157) {
        reward = -3.0;
    } else if (delay_us <= 484+181) {
        reward = -4.0;
    } else if (delay_us <= 665+205) {
        reward = -5.0;
    } else if (delay_us <= 870+229) {
        reward = -6.0;
    } else if (delay_us <= 1099) {
        reward = -7.0;
    } else {
        // High delay, fixed penalty
        reward = -8.0;
    }
    // === Layer 1 RL 更新 (块类别预测) ===
    // Layer 1使用三个页面类型下的奖励平均值
    // 基于TRE模块返回的三个页面类型RBER值计算平均奖励

    double layer1_reward = reward;  // 默认使用当前奖励

    // 如果TRE数据中包含三个页面类型的RBER值，计算平均奖励
    if (tre_data.rber_lp > 0.0 || tre_data.rber_mp > 0.0 || tre_data.rber_up > 0.0) {
        // 基于三个页面类型的RBER值计算各自的奖励
        double reward_lp = calculate_reward_from_rber(tre_data.delay_us_rber_lp);
        double reward_mp = calculate_reward_from_rber(tre_data.delay_us_rber_mp);
        double reward_up = calculate_reward_from_rber(tre_data.delay_us_rber_up);

        // 计算三个页面类型奖励的平均值
        layer1_reward = (reward_lp + reward_mp + reward_up) / 3.0;
    }

    // 使用 S-A-R-S' 四元组更新Q表:
    // S: 写操作前的状态 (last_state_layer1)
    // A: 写操作采取的块类别动作 (last_action_layer1)
    // R: 三个页面类型下的奖励平均值 (layer1_reward)
    // S': 写操作后的状态 (next_state_layer1)
    if (use_experience_replay) {
        // 使用经验回放机制
        agent_layer1->store_experience(info.last_state_layer1, info.last_action_layer1,
                                     layer1_reward, info.next_state_layer1, lpa);
    } else {
        // 直接更新Q表
        agent_layer1->update_q_table(info.last_state_layer1, info.last_action_layer1,
                                   layer1_reward, info.next_state_layer1);
    }

    // === Layer 2 RL 更新 (页面类型预测) ===
    // 使用 S-A-R-S' 四元组更新Q表:
    // S: 基于预测块类别的状态 (last_state_layer2)
    // A: 写操作采取的页面类型动作 (last_action_layer2)
    // R: 读操作得到的奖励 (reward) - 与Layer 1相同的奖励
    // S': 写操作后基于实际块类别的状态 (next_state_layer2)
    if (use_experience_replay) {
        // 使用经验回放机制
        agent_layer2->store_experience(info.last_state_layer2, info.last_action_layer2,
                                     reward, info.next_state_layer2, lpa);
    } else {
        // 直接更新Q表
        agent_layer2->update_q_table(info.last_state_layer2, info.last_action_layer2,
                                   reward, info.next_state_layer2);
    }

    // 更新统计信息
    total_reward += reward;

    // 重置状态和动作信息，避免重复更新
    info.last_state_layer1 = -1;
    info.last_action_layer1 = -1;
    info.next_state_layer1 = -1;
    info.last_state_layer2 = -1;
    info.last_action_layer2 = -1;
    info.next_state_layer2 = -1;
}

// === 置信度分数联合决策机制实现 ===

double RLManager::calculate_confidence_score(RLAgent* agent, int state, int action, const std::vector<bool>& mask)
{
    if (agent == nullptr || action < 0 || action >= agent->get_action_count()) {
        return 0.0;
    }

    // 获取当前状态下所有可用动作的Q值
    std::vector<double> q_values;
    double total_q_value = 0.0;
    int valid_actions = 0;

    for (int a = 0; a < agent->get_action_count(); a++) {
        if (mask[a]) {  // 只考虑可用的动作
            double q_val = agent->get_q_value(state, a);
            q_values.push_back(q_val);
            total_q_value += q_val;
            valid_actions++;
        }
    }

    if (valid_actions == 0 || total_q_value <= 0.0) {
        return 0.0;  // 没有有效动作或总Q值为负/零
    }

    // 计算置信度分数：当前动作的Q值 / 所有可用动作的Q值总和
    double action_q_value = agent->get_q_value(state, action);
    double confidence = action_q_value / total_q_value;

    return std::max(0.0, confidence);  // 确保置信度非负
}

// 基于RBER值计算奖励
double RLManager::calculate_reward_from_rber(double delay_us) {
    // 将RBER值转换为延时（微秒），然后计算奖励
    // 这里使用简化的RBER到延时的映射关系
    // 实际应用中可以根据具体的TRE模型进行调整

    double reward = 0.0;
    if (delay_us <= 85) {
        // No delay, give highest reward
        reward = 3.0;
    } else if (delay_us <= 85+109) {
        reward = -1.0;
        // Low delay, map to [3, 0] range
        //reward = round(3.0 - (delay_us / 85.0) * 3.0);
    } else if (delay_us <= 194+133) {
        reward = -2.0;
    } else if (delay_us <= 327+157) {
        reward = -3.0;
    } else if (delay_us <= 484+181) {
        reward = -4.0;
    } else if (delay_us <= 665+205) {
        reward = -5.0;
    } else if (delay_us <= 870+229) {
        reward = -6.0;
    } else if (delay_us <= 1099) {
        reward = -7.0;
    } else {
        // High delay, fixed penalty
        reward = -8.0;
    }
    return reward;
}

std::pair<int, int> RLManager::make_joint_decision(int state_layer1,
                                                  const std::vector<bool>& mask_layer1,
                                                  const PlaneID& plane_id)
{
    // === 置信度联合决策机制 ===
    // 此函数只在非探索阶段调用，专注于基于Q表值的联合决策

    const double CONFIDENCE_THRESHOLD = 0.2;  // 置信度阈值，低于此值使用各自独立决策

    double max_joint_confidence = 0.0;
    int best_block_action = -1;
    int best_page_action = -1;

    // 遍历所有可能的动作组合
    for (int block_action = 0; block_action < NUM_ACTIONS_LAYER1; block_action++) {
        if (!mask_layer1[block_action]) continue;  // 跳过不可用的块类型动作

        // 为当前块类型获取精确的页面类型mask
        BlockClass current_block_class = static_cast<BlockClass>(block_action);
        std::vector<bool> specific_page_mask = get_available_page_types_mask(plane_id, current_block_class);

        for (int page_action = 0; page_action < NUM_ACTIONS_LAYER2; page_action++) {
            // 使用特定块类型的页面mask，确保块页类型组合真实可用
            if (!specific_page_mask[page_action]) continue;  // 跳过在该块类型下不可用的页面类型动作

            // 计算第一层RL的置信度分数
            double confidence_layer1 = calculate_confidence_score(agent_layer1, state_layer1, block_action, mask_layer1);

            // 计算第二层RL的置信度分数（使用特定块类型的状态）
            int specific_state_layer2 = calculate_state_index_layer2(current_block_class, plane_id);
            double confidence_layer2 = calculate_confidence_score(agent_layer2, specific_state_layer2, page_action, specific_page_mask);

            // 联合置信度 = 第一层置信度 × 第二层置信度
            double joint_confidence = confidence_layer1 * confidence_layer2;

            // 记录最大联合置信度的动作组合
            if (joint_confidence > max_joint_confidence) {
                max_joint_confidence = joint_confidence;
                best_block_action = block_action;
                best_page_action = page_action;
            }
        }
    }

    // 判断是否使用联合决策
    if (max_joint_confidence > CONFIDENCE_THRESHOLD && best_block_action != -1 && best_page_action != -1) {
        // 使用联合决策结果
        return std::make_pair(best_block_action, best_page_action);
    } else {
        // 置信度不足，使用各自独立决策
        int independent_block_action = agent_layer1->select_action_with_mask(state_layer1, mask_layer1, true);

        // 为选择的块类型计算Layer 2状态和mask
        BlockClass selected_block_class = static_cast<BlockClass>(independent_block_action);
        int independent_state_layer2 = calculate_state_index_layer2(selected_block_class, plane_id);
        std::vector<bool> independent_page_mask = get_available_page_types_mask(plane_id, selected_block_class);

        int independent_page_action = agent_layer2->select_action_with_mask(independent_state_layer2, independent_page_mask, true);
        return std::make_pair(independent_block_action, independent_page_action);
    }
}

// Delayed Q-table update methods implementation
void RLAgent::update_q_table_delayed(int state, int action, double reward, int next_state) {
    // Update the copy Q-table instead of the decision Q-table
    // Find max Q-value in next state efficiently using the update Q-table
    const auto& next_q_values = q_table_update[next_state];
    double max_next_q = next_q_values.empty() ? 0.0 : next_q_values[0];

    // Manual loop is faster than std::max_element for small arrays
    for (size_t i = 1; i < next_q_values.size(); i++) {
        if (next_q_values[i] > max_next_q) {
            max_next_q = next_q_values[i];
        }
    }

    // Q-Learning update formula with direct computation on update Q-table
    const double target = reward + gamma * max_next_q;
    const double current = q_table_update[state][action];
    q_table_update[state][action] = current + alpha * (target - current);
}

void RLAgent::sync_q_tables() {
    // Copy update Q-table to decision Q-table at epoch end
    for (size_t i = 0; i < q_table.size(); i++) {
        for (size_t j = 0; j < q_table[i].size(); j++) {
            q_table[i][j] = q_table_update[i][j];
        }
    }

    // Reset epoch read count
    current_epoch_reads = 0;
}

void RLAgent::increment_read_count() {
    read_count++;
    current_epoch_reads++;

    // Check if epoch has ended and sync Q-tables if needed
    if (is_epoch_end()) {
        sync_q_tables();
    }
}

bool RLAgent::is_epoch_end() const {
    return current_epoch_reads >= epoch_size;
}

// New block management methods for improved RL action execution

// 执行RL动作：根据预测的块类型和页类型找到对应的链表头节点对应的块写入
RLBlock* RLManager::execute_rl_action(const PlaneID& plane_id, BlockClass predicted_block_class, PageType predicted_page_type) {
    auto plane_it = plane_block_lists.find(plane_id);
    if (plane_it == plane_block_lists.end()) {
        return nullptr;  // 平面不存在
    }

    PlaneBlockList& plane_list = plane_it->second;
    int class_idx = static_cast<int>(predicted_block_class);
    int page_type_idx = static_cast<int>(predicted_page_type);

    // 获取对应链表
    auto& target_list = plane_list.block_lists[class_idx][page_type_idx];

    // 如果链表为空，返回nullptr
    if (target_list.blocks.empty()) {
        return nullptr;
    }

    // 获取链表头节点对应的块
    RLBlock* selected_block = target_list.blocks.front();

    // 从链表中移除该节点
    target_list.blocks.pop_front();

    return selected_block;
}

// 将块移动到下一个页面类型的链表尾部
void RLManager::move_block_to_next_page_type_in_plane(const PlaneID& plane_id, RLBlock* block, BlockClass block_class, PageType current_page_type) {
    auto plane_it = plane_block_lists.find(plane_id);
    if (plane_it == plane_block_lists.end()) {
        return;  // 平面不存在
    }

    PlaneBlockList& plane_list = plane_it->second;
    int class_idx = static_cast<int>(block_class);
    int current_page_type_idx = static_cast<int>(current_page_type);

    // 注意：块已经在execute_rl_action中从当前链表移除了，所以这里不需要再次移除

    // 获取下一个页面类型
    PageType next_page_type = get_next_page_type_for_block(block);
    int next_page_type_idx = static_cast<int>(next_page_type);

    // 将块添加到下一个页面类型链表的尾部
    plane_list.block_lists[class_idx][next_page_type_idx].blocks.push_back(block);
}

// 处理写满的块：移出对应类型块池，放入已满块池，并从未写块池中拿一个新块
void RLManager::handle_full_block_in_plane(const PlaneID& plane_id, RLBlock* full_block, BlockClass block_class) {
    auto plane_it = plane_block_lists.find(plane_id);
    if (plane_it == plane_block_lists.end()) {
        return;  // 平面不存在
    }

    PlaneBlockList& plane_list = plane_it->second;

    // 将满块放入已满块池
    plane_list.full_blocks.push_back(full_block);

    // 从未写块池中获取一个新块
    RLBlock* new_block = get_new_block_from_unwritten_pool(plane_id, block_class);

    if (new_block != nullptr) {
        // 设置新块的类别
        new_block->block_class = block_class;

        // 将新块添加到同类型池的LP链表（全新的块第一个待写页面肯定是LP）
        int class_idx = static_cast<int>(block_class);
        plane_list.block_lists[class_idx][0].blocks.push_back(new_block);  // 0 = LP
    }
}

// 从未写块池中获取一个新块
RLBlock* RLManager::get_new_block_from_unwritten_pool(const PlaneID& plane_id, BlockClass block_class) {
    auto plane_it = plane_block_lists.find(plane_id);
    if (plane_it == plane_block_lists.end()) {
        return nullptr;  // 平面不存在
    }

    PlaneBlockList& plane_list = plane_it->second;

    // 如果未写块池为空，返回nullptr
    if (plane_list.unwritten_blocks.empty()) {
        return nullptr;
    }

    // 获取未写块池中的第一个块
    RLBlock* new_block = plane_list.unwritten_blocks.back();
    plane_list.unwritten_blocks.pop_back();

    return new_block;
}

// 获取块的下一个页面类型
PageType RLManager::get_next_page_type_for_block(RLBlock* block) {
    // 根据块的下一个写入页面索引确定页面类型
    // 页面类型循环：LP -> MP -> UP -> LP -> ...
    PageType types[] = {PageType::LP, PageType::MP, PageType::UP};
    return types[block->next_write_page_index % 3];
}

// 检查块是否已满
bool RLManager::is_block_full(RLBlock* block) {
    return block->free_pages == 0;
}

} // namespace SSD_Components