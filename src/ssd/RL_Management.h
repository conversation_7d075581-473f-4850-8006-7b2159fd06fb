#ifndef RL_MANAGEMENT_H
#define RL_MANAGEMENT_H

#include <vector>
#include <queue>
#include <map>
#include <unordered_map>
#include <unordered_set>        // Added for set operations
#include <cmath>
#include <random>
#include <algorithm>
#include <memory>
#include <climits>
#include <array>                // Added for performance optimizations
#include <thread>               // Added for thread-local RNG support
#include "../sim/Sim_Defs.h"
#include "Flash_Block_Manager_Base.h"
#include "NVM_Transaction_Flash_RD.h"
#include "NVM_Transaction_Flash_WR.h"
#include "NVM_Transaction_Flash_ER.h"
#include "../nvm_chip/flash_memory/Physical_Page_Address.h"
#include "../exec/Execution_Parameter_Set.h"  // Added for XML configuration access
#include <fstream>
#include <list>

namespace SSD_Components {
    
    // Forward declaration for TRE_Module
    class TRE_Module;
    
    // Constants for two-layer RL model
    // First layer RL: predicts block class only
    const int NUM_STATES_LAYER1 = 27;  // 3*3*3 = 27 (simplified state dimensions without neighbor access and page size)
    const int NUM_ACTIONS_LAYER1 = 3;   // 3 block classes only (HOT, WARM, COLD)

    // Second layer RL: predicts page type based on block class
    const int NUM_STATES_LAYER2 = 9;    // 3 block classes * 3 average idle time categories = 9
    const int NUM_ACTIONS_LAYER2 = 3;   // 3 page types (LP, MP, UP)

    const int NUM_BLOCK_CLASSES = 3;  // HOT, WARM, COLD
    const int NUM_PAGE_TYPES = 3;  // LP, MP, UP

    // Legacy constants for compatibility
    const int NUM_STATES = NUM_STATES_LAYER1;  // For backward compatibility
    const int NUM_ACTIONS = NUM_ACTIONS_LAYER1 * NUM_ACTIONS_LAYER2;  // Total combinations for statistics

    // Constants
    const flash_block_ID_type NO_BLOCK = UINT_MAX; // Special value indicating no block assigned

    // Page types
    enum class PageType {
        LP,  // Lower Page
        MP,  // Middle Page
        UP   // Upper Page
    };

    // Block classes
    enum class BlockClass {
        HOT = 0,
        WARM = 1,
        COLD = 2,
        STATELESS = 3
    };

    // SSD Block representation for RL model
    class RLBlock {
    public:
        RLBlock(flash_block_ID_type id);
        
        flash_block_ID_type block_id;           // Global block ID
        std::vector<LPA_type> pages;            // Store page LPAs
        unsigned int free_pages;                // Number of free pages
        unsigned int pec;                       // Program/Erase Count
        sim_time_type last_write_time;          // Last write time
        sim_time_type last_access_time;         // Last access time
        std::map<LPA_type, unsigned int> page_access_count;  // Page access count
        BlockClass block_class;                 // Block class
        sim_time_type idle_time;                // Idle time
        unsigned int base_pec;                  // Base PEC value
        std::map<LPA_type, sim_time_type> pages_last_access_time;  // Pages' last access time
        unsigned int next_write_page_index;     // Next write page index
        unsigned int total_access_count;        // Total block access count

        // Access frequency tracking for joint scoring (最近50000次IO的访问频率跟踪)
        std::deque<sim_time_type> recent_access_times;  // 最近访问时间队列，用于计算访问频率
        unsigned int access_count_in_window;            // 在当前窗口内的访问次数

        // New plane identification fields for plane-level management
        flash_channel_ID_type channel_id;      // Channel ID this block belongs to
        flash_chip_ID_type chip_id;            // Chip ID this block belongs to
        flash_die_ID_type die_id;              // Die ID this block belongs to
        flash_plane_ID_type plane_id;          // Plane ID this block belongs to
        stream_id_type stream_id;              // Stream ID for block allocation
        
        // 获取块在SSD层级结构中的唯一标识符 (Channel-Chip-Die-Plane-BlockID)
        std::string get_unique_id() const {
            return std::to_string(channel_id) + "-" + 
                   std::to_string(chip_id) + "-" + 
                   std::to_string(die_id) + "-" + 
                   std::to_string(plane_id) + "-" + 
                   std::to_string(block_id);
        }
        
        // 获取块在其所属平面内的本地ID - 修改为从0开始的连续编码
        flash_block_ID_type get_local_block_id(unsigned int blocks_per_plane) const {
            // 计算该块在其所属平面内的本地ID，确保从0开始连续编码
            // 使用整数除法获取平面索引，然后计算平面内的偏移
            unsigned int plane_index = block_id / blocks_per_plane;
            return block_id - (plane_index * blocks_per_plane);
        }
        
        bool write_page(LPA_type page_id, sim_time_type timestamp, bool update_access_time = true);
        bool read_page(LPA_type page_id, sim_time_type timestamp, bool update_access_time = true);
        sim_time_type get_idle_time(sim_time_type current_time);
        PageType get_next_page_type();
        void erase_block(sim_time_type timestamp);
    };

    // Page info for the RL model
    class RLPageInfo {
    public:
        RLPageInfo();
        
        std::vector<sim_time_type> access_timestamps;  // Access timestamps
        BlockClass last_block_class;              // Last allocated block class
        sim_time_type last_write_time;            // Last write time
        flash_block_ID_type last_block_id;        // Last allocated block ID
        unsigned int write_count;                 // Write count

        // Two-layer RL state and action information
        // Layer 1 (Block class prediction)
        int last_state_layer1;                    // Layer 1: Last state (S - State before write operation)
        int last_action_layer1;                   // Layer 1: Last action (A - Block class action)
        int next_state_layer1;                    // Layer 1: Next state (S' - State after write operation)

        // Layer 2 (Page type prediction)
        int last_state_layer2;                    // Layer 2: Last state (S - State based on predicted block class)
        int last_action_layer2;                   // Layer 2: Last action (A - Page type action)
        int next_state_layer2;                    // Layer 2: Next state (S' - State after write operation)

        // Legacy fields for compatibility
        int last_state;                           // Legacy: Last state (S - State before write operation)
        int last_action;                          // Legacy: Last action (A - Combined action)
        int next_state;                           // Legacy: Next state (S' - State after write operation)

        sim_time_type last_block_access_time;     // Last block access time
        unsigned int num_pages;                   // Number of pages
        PageType page_type;                       // Page type
        int last_state_index;                     // Last state index for state transitions
    };

    // Reinforcement Learning Agent
    class RLAgent {
    public:
        RLAgent(int num_states, int num_actions, bool use_replay = false);

        std::vector<std::vector<double>> q_table;  // Q-table for decision making
        std::vector<std::vector<double>> q_table_update;  // Q-table copy for updates
        double epsilon;                            // Exploration rate
        double alpha;                              // Learning rate
        double gamma;                              // Discount factor
        std::vector<std::tuple<int, int, double, int, LPA_type>> replay_buffer;  // Experience replay buffer with LPA tracking
        unsigned int min_replay_size;              // Minimum replay size
        unsigned int replay_batch_size;            // Replay batch size
        double min_epsilon;                        // Minimum epsilon
        double epsilon_decay;                      // Epsilon decay
        unsigned int step_count;                   // Step count
        unsigned int read_count;                   // Read count
        std::vector<double> q_changes;             // Q-value changes
        bool use_experience_replay;                // Use experience replay

        // Delayed Q-table update management
        unsigned int epoch_size;                   // Number of read operations per epoch (default: 2000)
        unsigned int current_epoch_reads;          // Current epoch read count
        bool use_delayed_update;                   // Whether to use delayed Q-table updates

        int select_action_with_mask(int state, const std::vector<bool>& action_mask,  bool is_after_joint);
        int select_random_action_with_mask(int state, const std::vector<bool>& action_mask);  // 随机选择动作（探索）
        int select_greedy_action_with_mask(int state, const std::vector<bool>& action_mask);  // 贪婪选择动作（利用）
        bool should_explore() const;  // 判断是否应该探索
        void update_q_table(int state, int action, double reward, int next_state);
        double store_experience(int state, int action, double reward, int next_state, LPA_type lpa = NO_LPA);
        double replay(unsigned int batch_size);
        double get_avg_q_change(unsigned int window = 100);

        // Delayed Q-table update methods
        void update_q_table_delayed(int state, int action, double reward, int next_state);  // Update the copy Q-table
        void sync_q_tables();  // Copy update Q-table to decision Q-table at epoch end
        void increment_read_count();  // Increment read count and check for epoch end
        bool is_epoch_end() const;  // Check if current epoch has ended
        
        // Optimized methods for performance
        void cached_update_q_table(int state, int action, double reward, int next_state, double& max_q_change);
        inline double fast_calculate_q_change(double old_value, double new_value);

        // Getter methods for confidence calculation
        int get_action_count() const { return q_table.empty() ? 0 : static_cast<int>(q_table[0].size()); }
        double get_q_value(int state, int action) const {
            if (state >= 0 && state < static_cast<int>(q_table.size()) &&
                action >= 0 && action < static_cast<int>(q_table[state].size())) {
                return q_table[state][action];
            }
            return 0.0;
        }
    };

    // Plane identifier for plane-level block pools
    struct PlaneID {
        flash_channel_ID_type channel_id;
        flash_chip_ID_type chip_id;
        flash_die_ID_type die_id;
        flash_plane_ID_type plane_id;
        
        // Define equality operator for map/set usage
        bool operator==(const PlaneID& other) const {
            return channel_id == other.channel_id && 
                   chip_id == other.chip_id && 
                   die_id == other.die_id && 
                   plane_id == other.plane_id;
        }
        
        // Define less than operator for map/set usage
        bool operator<(const PlaneID& other) const {
            if (channel_id != other.channel_id)
                return channel_id < other.channel_id;
            if (chip_id != other.chip_id)
                return chip_id < other.chip_id;
            if (die_id != other.die_id)
                return die_id < other.die_id;
            return plane_id < other.plane_id;
        }
    };
    
    // Structure for block lists in each plane
    struct PlaneBlockList {
        // List structure for each block class and page type combination
        struct BlockList {
            std::list<RLBlock*> blocks;         // Linked list of blocks, ordered by LRU
            unsigned int target_count;           // Target number of blocks for this list

            BlockList() : target_count(12) {}   // Default 12 blocks per list per plane (as required)
        };

        // 3D array of block lists [block_class][page_type] for each plane
        // 总共9个链表：3个块类型（热、暖、冷） × 3个页类型（LP、MP、UP）
        BlockList block_lists[NUM_BLOCK_CLASSES][NUM_PAGE_TYPES];

        // Block pools for this plane
        std::vector<RLBlock*> unwritten_blocks;    // 未写块池（原stateless_blocks）
        std::vector<RLBlock*> full_blocks;         // 已写满块池
        
        // 平面级别的访问计数和最后分类时间
        unsigned int access_count;                // 平面访问计数
        sim_time_type last_classify_time;        // 平面最后分类时间
        
        PlaneBlockList() : access_count(0), last_classify_time(0) {
            // Initialize target counts for each list
            for (int class_idx = 0; class_idx < NUM_BLOCK_CLASSES; class_idx++) {
                for (int page_type_idx = 0; page_type_idx < NUM_PAGE_TYPES; page_type_idx++) {
                    // 每个块类型（热、暖、冷）维护固定12个可供写入的块
                    // 这12个块在3个页类型（LP、MP、UP）之间分配
                    if (page_type_idx == 0) { // LP页类型
                        block_lists[class_idx][page_type_idx].target_count = 12; // 所有新块都从LP开始
                    } else { // MP和UP页类型
                        block_lists[class_idx][page_type_idx].target_count = 0;  // 初始为0，块会从LP移动过来
                    }
                }
            }
        }
    };

    // SSD Simulator with RL model
    class RLManager {
    public:
        // Debug flags for detailed logging
        bool enable_write_logging = false;  // Flag to enable detailed write request logging
        bool enable_io_stats_logging = true;  // Flag to enable periodic IO statistics logging (default enabled)
        bool enable_classification_logging = false;  // Flag to enable logging of block classification before and after
        
        // Flag to control state classification strategy
        bool use_dynamic_state_classification = false;  // False=固定值策略, True=动态策略
        
        // Number of pages per block
        unsigned int pages_per_block;
        
        // 最后实际使用的页面类型，用于追踪动作执行
        PageType last_actual_page_type = PageType::LP;
        
        // RL模型所使用的基础PEC
        unsigned int rl_base_pec = 3000;
        
        RLManager(unsigned int total_blocks, unsigned int pages_per_block, Flash_Block_Manager_Base* block_manager, bool use_exp_replay = true);
        ~RLManager();
        
        // Static method to get the singleton instance
        static RLManager* Get_RL_Manager() {
            return instance;
        }
        
        // Static method to initialize the singleton instance
        static void Initialize_RL_Manager(RLManager* manager) {
            instance = manager;
        }
        
        // 初始化函数
        void initialize();
        void initialize_parameters_from_xml();
        
        // 获取所有平面ID的辅助函数
        std::vector<PlaneID> get_all_plane_ids(unsigned int channel_count, unsigned int chip_count, 
                                      unsigned int die_count, unsigned int plane_count);
                                      
        // 初始化平面块链表
        void initialize_plane_block_lists(unsigned int channel_count, unsigned int chip_count, 
                                  unsigned int die_count, unsigned int plane_count);
    
        // 在指定平面中获取块用于写入，添加 stream_id 参数
        RLBlock* get_block_for_write_in_plane(const PlaneID& plane_id, BlockClass block_class, PageType page_type, stream_id_type stream_id);
    

    
        // 计算块温度得分
        double calculate_block_temperature_score(RLBlock* block);

        // 联合评分机制：访问频率 + idle时间
        double calculate_joint_score(RLBlock* block);
        double calculate_access_frequency_score(RLBlock* block);
        void update_block_access_frequency(flash_block_ID_type block_id, sim_time_type access_time);
        void cleanup_old_access_records(sim_time_type current_time);
    
        // 将LPA映射到离散状态
        int discretize_state(LPA_type lpa, sim_time_type current_time);
    
        // 更新块访问统计
        void update_block_access_stats(flash_block_ID_type block_id);
    
        // PageType转换为字符串
        std::string page_type_to_string(PageType type);
    
        // 对平面内的块进行分类，保持各类型块数量固定
        void classify_blocks_in_plane(const PlaneID& plane_id);
    
        // 确定块当前的页面类型
        PageType determine_block_current_page_type(RLBlock* block);
    
        // 处理写入请求，更新模型并返回是否成功处理
        bool process_write_request(LPA_type lpa, sim_time_type current_time, unsigned int num_pages, const NVM::FlashMemory::Physical_Page_Address& address, stream_id_type stream_id);
        
        // 新的页面分配接口，替代process_write_request作为主要接口
        bool allocate_page_based_on_rl(LPA_type lpa, sim_time_type current_time, unsigned int num_pages, NVM::FlashMemory::Physical_Page_Address& address, stream_id_type stream_id);
    

        
        // 从物理地址获取平面ID
        PlaneID get_plane_id(const NVM::FlashMemory::Physical_Page_Address& address);

        // Main API functions
        std::map<std::string, double> process_read_request(LPA_type lpa, sim_time_type current_time, 
                                                         NVM_Transaction_Flash_RD* read_transaction = nullptr, 
                                                         uint64_t delay_us_rber = 0);
        void handle_block_erase(const NVM::FlashMemory::Physical_Page_Address& address, sim_time_type time);
        
        // New methods for XML config and statistics
        double get_tre_ratio() const { return total_read_count > 0 ? static_cast<double>(tre_count) / total_read_count : 0.0; }
        unsigned int get_tre_count() const { return tre_count; }
        unsigned int get_total_read_count() const { return total_read_count; }
        
        // Performance-optimized methods
        inline int fast_calculate_state_index(int long_term_count, int weighted_access_interval,
                                           int neighbor_access_state, int size_category, int block_class);
        inline int fast_calculate_state_index_simplified(int long_term_count, int weighted_access_interval, int block_class);
        
        // Statistics
        unsigned int write_count;
        unsigned int read_count;
        unsigned int read_miss_count;
        unsigned int write_miss_count;
        unsigned int page_type_mismatch_count;
        unsigned int page_type_match_count;       // Count of successful page type matches
        unsigned int page_type_search_attempts;   // Count of attempts to find matching page type
        unsigned int tre_count;           // Count of TRE occurrences (RBER > 0.005)
        unsigned int total_read_count;    // Total read operations for calculating ratio
        double total_reward;
        double avg_pec;
        unsigned int total_io_count;
        unsigned int total_requests;
        
        // Page type statistics
        std::map<std::string, unsigned int> predicted_page_type_count;
        std::map<std::string, unsigned int> actual_page_type_count;
        
        // New constants for block pool configuration
        const unsigned int ACTIVE_POOL_SIZE = 32;  // Size of each active pool (hot/warm/cold)
        
        // Block management
        std::vector<RLBlock*> blocks;
        std::unordered_map<LPA_type, flash_block_ID_type> page_to_block;
        std::unordered_map<LPA_type, RLPageInfo> page_info;
        
        // LPA to PPA mapping table
        std::unordered_map<LPA_type, NVM::FlashMemory::Physical_Page_Address> lpa_to_ppa_mapping;
        
        // New plane-level block management
        std::map<PlaneID, PlaneBlockList> plane_block_lists;
        
        // Legacy global block pools - will be maintained for compatibility but not actively used
        std::vector<std::vector<RLBlock*>> block_pools;  // By block class: [0]=hot, [1]=warm, [2]=cold, [3]=stateless
        std::vector<std::vector<std::pair<sim_time_type, flash_block_ID_type>>> block_pools_by_access_time;
        
        // 目标块池大小 - 每个块类别(HOT/WARM/COLD)的目标块数
        unsigned int target_blocks_per_class;
        
        // 热块和暖块的阈值 - 动态调整
        double hot_threshold;
        double warm_threshold;

        // 工作负载监测变量
        double workload_intensity = 0.5;     // 当前工作负载强度，0-1
        sim_time_type last_intensity_update_time = 0;  // 最后更新工作负载强度的时间

        // 联合评分机制的访问频率跟踪（最近50000次IO）
        static const unsigned int ACCESS_FREQUENCY_WINDOW = 50000;  // 访问频率统计窗口大小
        std::deque<std::pair<sim_time_type, flash_block_ID_type>> global_access_history;  // 全局访问历史
        
        // 状态分类阈值
        struct {
            // 固定值策略阈值
            unsigned int fixed_access_count_thresholds[2] = {2, 4};  // 0-1, 2-3, 4+
            sim_time_type fixed_access_interval_threshold = 6 * 3600e9;  // 6小时，纳秒

            // 动态策略阈值
            unsigned int dynamic_access_count_threshold;  // 动态调整 (2-20次)
            sim_time_type dynamic_access_interval_threshold;  // 动态调整 (1-11小时)

            // 邻居访问范围参数
            unsigned int neighbor_range = 5;  // 固定策略下邻居范围为5
        } state_thresholds;

        // 邻居访问跟踪
        std::unordered_set<LPA_type> recent_accessed_lpas;  // 最近10000个IO内访问过的LPA集合
        std::queue<LPA_type> lpa_access_queue;  // 先进先出队列，用于移除旧的访问记录
        unsigned int neighbor_io_window = 10000;  // 邻居访问窗口大小
        
        // Access history
        std::deque<LPA_type> page_access_history;
        std::deque<flash_block_ID_type> recent_block_accesses;
        
        // Two-layer RL Agents
        RLAgent* agent_layer1;  // First layer: predicts block class
        RLAgent* agent_layer2;  // Second layer: predicts page type

        // Legacy agent pointer for backward compatibility
        RLAgent* agent;  // Points to agent_layer1 for compatibility
        
        // Classifier state
        sim_time_type current_time;
        sim_time_type last_classify_time;
        unsigned int classification_interval;
        unsigned int recalculate_threshold;
        
        // State statistics for two-layer RL
        // Layer 1 statistics (block class prediction)
        std::map<std::tuple<int, int, int, int, int>, unsigned int> state_vector_count_layer1;   // 5维状态向量统计（包含邻居访问状态）
        std::map<int, unsigned int> state_index_count_layer1;
        std::map<std::pair<int, int>, unsigned int> write_state_action_count_layer1;
        std::map<int, unsigned int> write_state_count_layer1;
        std::map<int, unsigned int> write_action_count_layer1;

        // Layer 2 statistics (page type prediction)
        std::map<int, unsigned int> state_index_count_layer2;
        std::map<std::pair<int, int>, unsigned int> write_state_action_count_layer2;
        std::map<int, unsigned int> write_state_count_layer2;
        std::map<int, unsigned int> write_action_count_layer2;

        // Combined statistics for compatibility
        std::map<std::tuple<int, int, int, int, int>, unsigned int> state_vector_count_new;   // Legacy compatibility
        std::map<std::tuple<int, int, int>, unsigned int> state_vector_count_simplified;   // Simplified 3D state vector statistics
        std::map<int, unsigned int> state_index_count;  // Legacy compatibility
        std::map<std::pair<int, int>, unsigned int> write_state_action_count;  // Legacy compatibility
        std::map<int, unsigned int> write_state_count;  // Legacy compatibility
        std::map<int, unsigned int> write_action_count;  // Legacy compatibility
        std::map<BlockClass, unsigned int> write_block_class_count;
        std::map<PageType, unsigned int> write_page_type_count;
        
        // Caching for improved performance
        struct {
            bool initialized = false;
            sim_time_type last_long_term_window = 0;
            double last_epc_ratio = -1.0;
            std::array<int, 5> multipliers = {1, 3, 6, 18, 54}; // Pre-computed multipliers for state index (3*2*2*3*3=108)
        } cache;

        struct {
            bool initialized = false;
            std::array<int, 3> multipliers = {1, 3, 9}; // Pre-computed multipliers for simplified state index (3*3*3=27)
        } cache_simplified;
        
        // Reference to MQSim's block manager
        Flash_Block_Manager_Base* mqsim_block_manager;
        
        // Helpers for mapping between enums and strings
        static PageType page_id_to_page_type(const NVM::FlashMemory::Physical_Page_Address& physical_address);
        
        // Configuration parameters from XML
        double tre_threshold;          // RBER threshold for triggering TRE
        unsigned int max_pec;          // Maximum program/erase cycles
        double solid_factor;           // Solid state factor for RBER calculation
        bool use_experience_replay;    // Whether to use experience replay for Q-value updates
        
        // Reference to the TRE module
        static TRE_Module* tre_module;
        
    private:
        // 阻止拷贝构造
        RLManager(const RLManager&);
        RLManager& operator=(const RLManager&);
        
        // Singleton instance
        static RLManager* instance;
        
        // New block management structure - 9 lists (3 block classes × 3 page types)
        static const int NUM_BLOCK_CLASSES = 3; // HOT, WARM, COLD
        static const int NUM_PAGE_TYPES = 3;    // LP, MP, UP
        
        // List structure for each block class and page type combination
        struct BlockList {
            std::list<RLBlock*> blocks;         // Linked list of blocks, ordered by LRU
            unsigned int target_count;           // Target number of blocks for this list
            
            BlockList() : target_count(8) {}    // Default 8 blocks per list
        };
        
        // 3D array of block lists [block_class][page_type]
        BlockList block_lists[NUM_BLOCK_CLASSES][NUM_PAGE_TYPES];
        
        // Helper methods for block list management
        RLBlock* get_block_for_write(BlockClass block_class, PageType page_type);
        void move_block_to_next_page_type(RLBlock* block, BlockClass block_class, PageType current_page_type);
        PageType get_block_current_page_type(RLBlock* block);

        // New block management methods for improved RL action execution
        RLBlock* execute_rl_action(const PlaneID& plane_id, BlockClass predicted_block_class, PageType predicted_page_type);
        void move_block_to_next_page_type_in_plane(const PlaneID& plane_id, RLBlock* block, BlockClass block_class, PageType current_page_type);
        void handle_full_block_in_plane(const PlaneID& plane_id, RLBlock* block, BlockClass block_class);
        RLBlock* get_new_block_from_unwritten_pool(const PlaneID& plane_id, BlockClass block_class);
        PageType get_next_page_type_for_block(RLBlock* block);
        bool is_block_full(RLBlock* block);
        
        // Stateless and full blocks pools
        std::vector<RLBlock*> stateless_blocks;
        std::vector<RLBlock*> full_blocks;
        
        // Dynamic parameters
        unsigned int dynamic_classification_interval;  // Dynamic interval for block classification
        unsigned int dynamic_active_pool_size;         // Dynamic size for active block pools
        //double workload_intensity;                     // Tracks workload intensity
        unsigned int io_window_size;                   // Window size for IO measurement
        std::deque<sim_time_type> recent_io_times;     // Recent IO operation timestamps
        
        // Experience replay parameters
        unsigned int max_replay_buffer_size;           // Maximum size of replay buffer
        
        // Moving average for access intervals instead of standard deviation
        struct MovingAverage {
            double value;                // Current moving average value 
            unsigned int count;          // Number of samples included
            double alpha;                // Weight of new samples (0-1)
            
            MovingAverage() : value(0.0), count(0), alpha(0.2) {}
            
            void update(double new_sample) {
                if (count == 0) {
                    value = new_sample;
                } else {
                    value = alpha * new_sample + (1 - alpha) * value;
                }
                count++;
            }
        };
        std::unordered_map<LPA_type, MovingAverage> page_interval_averages;

        // Helper functions for two-layer RL
        // Layer 1 functions (block class prediction)
        int calculate_state_index_layer1(const std::tuple<int, int, int, int, int>& state_vector);   // Layer 1: 5维状态向量（包含邻居访问状态）
        int calculate_state_index_layer1_simplified(const std::tuple<int, int, int>& state_vector);   // Layer 1: 简化的3维状态向量
        std::vector<bool> get_available_block_classes_mask(const PlaneID& plane_id);  // Layer 1: 获取可用块类别掩码

        // Layer 2 functions (page type prediction)
        int calculate_state_index_layer2(BlockClass predicted_block_class, const PlaneID& plane_id);  // Layer 2: 基于块类别和平均idle时间
        std::vector<bool> get_available_page_types_mask(const PlaneID& plane_id, BlockClass block_class);  // Layer 2: 获取可用页面类型掩码
        std::vector<bool> get_comprehensive_page_types_mask(const PlaneID& plane_id);  // Layer 2: 获取覆盖所有块类型的页面类型掩码
        double calculate_average_idle_time(const PlaneID& plane_id, BlockClass block_class);  // 计算指定块类别的平均idle时间
        int discretize_idle_time(double avg_idle_time);  // 将平均idle时间离散化为3个类别

        // Two-layer RL reward calculation and Q-table update
        void update_two_layer_rl_rewards(LPA_type lpa, double delay_us, const SSD_Components::TRE_Data& tre_data);  // 两层RL模型的奖励计算和Q表更新

        // Confidence-based joint decision making for two-layer RL
        double calculate_confidence_score(RLAgent* agent, int state, int action, const std::vector<bool>& mask);  // 计算置信度分数
        std::pair<int, int> make_joint_decision(int state_layer1,
                                               const std::vector<bool>& mask_layer1,
                                               const PlaneID& plane_id);  // 联合决策

        // Reward calculation helper functions
        double calculate_reward_from_rber(double rber);  // 基于RBER值计算奖励

        // Legacy functions for compatibility
        int calculate_state_index_new(const std::tuple<int, int, int, int, int>& state_vector);   // 兼容性函数，调用layer1
        bool check_neighbor_access(LPA_type lpa);  // 检查邻居LPA是否在最近访问过
        void update_neighbor_access_tracking(LPA_type lpa);  // 更新邻居访问跟踪
        void update_dynamic_neighbor_range();  // 动态更新邻居范围
        std::tuple<int, int, int, int, int> decode_state_index(int state_index);
        double calculate_rber(sim_time_type idle_time, PageType page_type, unsigned int pec);
        double calculate_dr_rber(sim_time_type idle_time, PageType page_type, unsigned int pec);
        uint64_t calculate_delay(double rber);
        double calculate_reward(double rber);
        double reward_to_delay(double reward);
        double calculate_hotness(RLBlock* block);
        RLBlock* select_block(BlockClass block_class);


        // Helper functions for dynamic parameters
        void update_classification_parameters();
        void update_workload_intensity(sim_time_type current_time);
        
        // Variables for IO statistics tracking
        unsigned int io_stats_counter = 0;
        unsigned int state_transition_count = 0;
        std::map<std::pair<int, int>, unsigned int> state_transition_pairs;
        std::map<int, unsigned int> action_execution_count;
        unsigned int page_type_match_total = 0;
    };
}

#endif // RL_MANAGEMENT_H 