#include "TRE_Module.h"
#include "../exec/Device_Parameter_Set.h"
#include "../sim/Engine.h"
#include "../sim/Sim_Defs.h"
#include <iostream>
#include <fstream>
#include <iomanip> // Required for std::setw and std::fixed

namespace SSD_Components
{
    TRE_Module::TRE_Module() :
        tre_enabled(false),  // Default to disabled until initialized
        BASE_PEC(3000),      // Default value - will be updated from config
        tre_threshold(0.005),// Default threshold
        tre_solid_factor(0.6),// Default solid factor
        logging_enabled(false), 
        total_read_count(0), 
        total_write_count(0)
    {
        // Initialize level counters
        for (int i = 0; i < 8; i++) {
            rber_level_counts[i] = 0;
            dr_rber_level_counts[i] = 0;
            only_rber_level_counts[i] = 0;
        }
    }

    TRE_Module::~TRE_Module()
    {
        if (logging_enabled && log_file.is_open()) {
            log_file.close();
        }
    }

    void TRE_Module::Initialize(const std::string& log_file_path)
    {
        // 从ssdconfig.xml配置文件读取TRE模块的关键参数

        // 读取TRE模块启用开关，赋值给tre_enabled变量
        // true表示启用TRE(Timeout-Read-Error)模块，false表示禁用
        if (Device_Parameter_Set::TRE_Enable.HasValue) {
            tre_enabled = Device_Parameter_Set::TRE_Enable.Value;
        } else {
            tre_enabled = true;  // 默认值：启用TRE模块
        }

        // 读取统一的基础PEC值，赋值给BASE_PEC变量（用于RBER计算和延时估算）
        BASE_PEC = Device_Parameter_Set::Base_PEC;

        // 硬编码的TRE模块参数（不从配置文件读取）
        tre_threshold = 0.005;      // TRE阈值：触发TRE的位错误率(RBER)阈值
        tre_solid_factor = 0.6;     // 固态因子：用于RBER到延时的转换计算
            
        // Initialize parameters
        
        // Simplified startup output
        std::cout << std::endl;
        std::cout << "=== TRE Module Status ===" << std::endl;
        std::cout << "• TRE Module: " << (tre_enabled ? "ENABLED" : "DISABLED") << std::endl;
        if (tre_enabled) {
            std::cout << "• Base PEC: " << BASE_PEC << std::endl;
            std::cout << "• Max PEC: 3000" << std::endl;
        }
        
        // Initialize the log file
        log_file.open(log_file_path);
        if (log_file.is_open()) {
            logging_enabled = true;
            log_file << "rber,delay_us_rber,rber_dr,delay_us_dr_rber,delay_us,Is_Read" << std::endl;
        }
    }

    double TRE_Module::Calculate_RBER(uint64_t idle_time_ns, Page_Type page_type, int pec)
    {
        // 性能优化：如果idle_time为0，直接返回0，避免后续计算
        if (idle_time_ns <= 0) {
            return 0.0;
        }
        
        // Convert idle time from ns to hours
        double T = idle_time_ns / 3.6e12; // 1h = 3.6e12 ns
        
        // 性能优化：如果转换后时间接近0，直接返回0
        if (T < 0.000001) {
            return 0.0;
        }

        // 性能优化：预先计算page type系数，避免重复的switch判断
        static const double PAGE_TYPE_COEFFS[3][2] = {
            {1.0, 0.0}, // LP: PageType0=1, PageType1=0
            {0.0, 1.0}, // MP: PageType0=0, PageType1=1
            {0.0, 0.0}  // UP: PageType0=0, PageType1=0
        };
        
        int page_type_idx = static_cast<int>(page_type);
        double PageType0 = PAGE_TYPE_COEFFS[page_type_idx][0];
        double PageType1 = PAGE_TYPE_COEFFS[page_type_idx][1];

        // Normalize PEC
        const double MIN_PEC = 0;
        const double MAX_PEC = 3000;
        int original_pec = pec;


        // test
        //PageType0 = 1;
        //PageType1 = 0;
        //original_pec = 1800;
        //T = 4;

        double normalized_pec = (original_pec - MIN_PEC) / (MAX_PEC - MIN_PEC);

        // 只使用混合模型
        // Calculate RBER based on PEC range
        if (original_pec <0 ) {
            // Low PEC formula
            double Ylow = (0.000112873013190101 + 0.000606593520728399 * (normalized_pec) 
              + 0.00000198974163505961 * PageType0 
              + 0.000000112595766534370 * PageType1) * T 
              + (0.00000000600695903183062 
                 + 0.00400912206217582 * (normalized_pec)
                 + 0.00206828176514910 * PageType0 
                 + 0.00246339421409020 * PageType1);

            //std::cout<<"Ylow"<<Ylow<<std::endl;
            //double Ylow = (0.00024521 + 0.00045251 * normalized_pec - 0.000076135 * PageType0 - 0.000092886 * PageType1) * T +(-0.0019272 + 0.006457 * normalized_pec + 0.0030701 * PageType0 + 0.0036465 * PageType1);
            return Ylow;
        }
        else if (original_pec >= 5000) {
            // High PEC formula
            double exp_factor = (-0.0821082610721852 
                    + 0.128958140284970 * (normalized_pec)
                    - 0.0207336236364037 * PageType0 
                    + 0.00903729782280047 * PageType1) * T;
            double Yhigh = (-0.00519411156037596 
               + 0.00114498442027409 * (normalized_pec)
               + 0.00180140363821254 * PageType0 
               + 0.00183569135182064 * PageType1 
               + 0.00116971163944702 * (normalized_pec) * T)
               * exp(exp_factor) 
               + 0.00603119862706745;
            //std::cout<<"Yhigh"<<Yhigh<<std::endl;
            //double exp_factor = (-0.016132 + 0.04662 * normalized_pec - 0.00024634 * PageType0 + 0.0015057 * PageType1) * T;
            //double Yhigh = (0.051036 - 0.0033402 * normalized_pec + 0.0012344 * PageType0 + 0.0015823 * PageType1) *exp(exp_factor) - 0.046654;
            return Yhigh;
        }
        else {
            // Mid PEC range, use weighted average
            //double w_epc = (original_pec - 1000) / 1000.0;
            double w_epc = (original_pec - 0) / 3000.0;
            // Calculate Ylow
            double Ylow = (0.000112873013190101 + 0.000606593520728399 * (normalized_pec) 
              + 0.00000198974163505961 * PageType0 
              + 0.000000112595766534370 * PageType1) * T 
              + (0.00000000600695903183062 
                 + 0.00400912206217582 * (normalized_pec)
                 + 0.00206828176514910 * PageType0 
                 + 0.00246339421409020 * PageType1);

            // Calculate Yhigh
            double exp_factor = (-0.0821082610721852 
                    + 0.128958140284970 * (normalized_pec)
                    - 0.0207336236364037 * PageType0 
                    + 0.00903729782280047 * PageType1) * T;
            double Yhigh = (-0.00519411156037596 
               + 0.00114498442027409 * (normalized_pec)
               + 0.00180140363821254 * PageType0 
               + 0.00183569135182064 * PageType1 
               + 0.00116971163944702 * (normalized_pec) * T)
               * exp(exp_factor) 
               + 0.00603119862706745;
    
            double midlle = (1 - w_epc) * Ylow + w_epc * Yhigh; 
            //std::cout<<"Ylow"<<Ylow<<std::endl;
            //std::cout<<"Yhigh"<<Yhigh<<std::endl;
            //std::cout<<"midlle"<<midlle<<std::endl;
            // Weighted average
            return midlle;
        }
    }
    
    double TRE_Module::Calculate_DR_RBER(uint64_t idle_time_ns, Page_Type page_type, int pec)
    {
        // Convert idle time from ns to hours
        double T = idle_time_ns / 3.6e12; // 1h = 3.6e12 ns
        
        // Protection against invalid time values
        if (T <= 0) {
            return 0.0;
        }
        

        // Take natural logarithm of time
        //double ln_T = log(T);
        // Set page type dummy variables
        double PageType0 = 0, PageType1 = 0;
        switch (page_type) {
            case Page_Type::LP: // Lower Page (LSB/SLC)
                PageType0 = 1;
                PageType1 = 0;
                break;
            case Page_Type::MP: // Middle Page (CSB/MLC)
                PageType0 = 0;
                PageType1 = 1;
                break;
            case Page_Type::UP: // Upper Page (MSB/TLC)
                PageType0 = 0;
                PageType1 = 0;
                break;
        }

        // test
        //PageType0 = 0;
        //PageType1 = 0;
        //T = 26;
        //pec = 2900;

        // Normalize PEC
        const double MIN_PEC = 0;
        const double MAX_PEC = 3000;
        double normalized_pec = (pec - MIN_PEC) / (MAX_PEC - MIN_PEC);

        
        // 时间指数部分
        double exponent_T = 0.242263175096745 
                        + 0.682537551950320 * normalized_pec
                        + 0.0606063129174000 * PageType0
                        + 0.178996816279455 * PageType1;
        
        // 指数项部分
        double exp_term = -7.48335707556119 
                        + 0.518655053076237 * normalized_pec
                        - 0.156103044259520 * PageType0
                        - 0.482358482458141 * PageType1;
        
        // 计算最终rBER
        double dr_rber = pow(T, exponent_T) * exp(exp_term);   
        //std::cout<<"rber"<<rber<<std::endl;
        return dr_rber;
    }   

    /*
    uint64_t TRE_Module::Calculate_Delay(double rber)
    {
        const double solid_factor = 0.6;
        double delay = 0;

        // Calculate delay based on RBER
        if (rber < 0.005) {
            delay = 85 * solid_factor + (rber / 0.005) * 85 * (1 - solid_factor);
        }
        else if (rber < 0.006) {
            delay = 85 + 109 * solid_factor + ((rber - 0.005) / 0.001) * 109 * (1 - solid_factor);
        }
        else if (rber < 0.008) {
            delay = 194 + 133 * solid_factor + ((rber - 0.006) / 0.002) * 133 * (1 - solid_factor);
        }
        else if (rber < 0.009) {
            delay = 327 + 157 * solid_factor + ((rber - 0.008) / 0.001) * 157 * (1 - solid_factor);
        }
        else if (rber < 0.01) {
            delay = 484 + 181 * solid_factor + ((rber - 0.009) / 0.001) * 181 * (1 - solid_factor);
        }
        else if (rber < 0.012) {
            delay = 665 + 205 * solid_factor + ((rber - 0.01) / 0.002) * 205 * (1 - solid_factor);
        }
        else if (rber <= 0.013) {
            delay = 870 + 229 * solid_factor + ((rber - 0.012) / 0.001) * 229 * (1 - solid_factor);
        }
        else { // rber > 0.013
            //delay = 1099 + ((rber - 0.013) / 0.001) * 229;
            delay = 1099;
        }

        return static_cast<uint64_t>(delay);
    }*/
    
    uint64_t TRE_Module::Calculate_Delay(double rber)
    {
        double delay = 0;
        double rber_off = 0.0;
        // Calculate delay based on RBER
        if (rber < 0.005-rber_off) {
            delay = 85;
        }
        else if (rber < 0.006-rber_off) {
            delay = 85 + 109;
        }
        else if (rber < 0.008-rber_off) {
            delay = 194 + 133;
        }
        else if (rber < 0.009-rber_off) {
            delay = 327 + 157;
        }
        else if (rber < 0.01-rber_off) {
            delay = 484 + 181;
        }
        else if (rber < 0.012-rber_off) {
            delay = 665 + 205;
        }
        else if (rber <= 0.013) {
            delay = 870 + 229 ;
        }
        else { // rber > 0.013
            //delay = 1099 + ((rber - 0.013) / 0.001) * 229;
            delay = 1099;
        }

        return static_cast<uint64_t>(delay);
    }
    
    Page_Type TRE_Module::Get_Page_Type(flash_page_ID_type page_id)
    {
        // Determine page type based on page_id
        // This is a simplified approach; in real TLC flash, the page type relationship can be more complex
        
        // Assuming page ID mapping similar to what's described in the code:
        // For TLC: 
        //   LP (Lower Page): page_id % 3 == 0
        //   MP (Middle Page): page_id % 3 == 1
        //   UP (Upper Page): page_id % 3 == 2
        switch (page_id % 3) {
            case 0: return Page_Type::LP;
            case 1: return Page_Type::MP;
            case 2: return Page_Type::UP;
            default: return Page_Type::UP; // Shouldn't happen
        }
    }

    uint64_t TRE_Module::Hash_Page_Address(const NVM::FlashMemory::Physical_Page_Address& addr)
    {
        // Use the unified block identifier directly, ignoring the page
        uint64_t block_id = 0;
        block_id += static_cast<uint64_t>(addr.ChannelID)  * 1E12;
        block_id += static_cast<uint64_t>(addr.ChipID) * 1E10;
        block_id += static_cast<uint64_t>(addr.DieID) * 1E8;
        block_id += static_cast<uint64_t>(addr.PlaneID) * 1E6;
        block_id += static_cast<uint64_t>(addr.BlockID);
        return block_id;
    }

    TRE_Data TRE_Module::Update_Page_Access(const NVM::FlashMemory::Physical_Page_Address& page_address, bool is_write)
    {
        TRE_Data tre_data;

        // If TRE is disabled, return default TRE_Data
        if (!tre_enabled) {
            return tre_data;
        }
        
        // Update operation counters
        if (is_write) {
            total_write_count++;
        } else {
            total_read_count++;
        }

        uint64_t now = Simulator->Time();
        uint64_t delay_us = 0;
        uint64_t delay_us_dr_rber = 0;
        uint64_t delay_us_rber = 0;
        double rber = 0.0;
        double rber_dr = 0.0;
        double only_rber = 0.0;
        int rber_level = 0;
        int rber_dr_level = 0;
        int only_rber_level = 0;
        /*
        if (is_write)
        {
            std::cout<<"page_address.BlockID: "<<page_address.BlockID<<std::endl;
            std::cout<<"page_address.PageID: "<<page_address.PageID<<std::endl;
        }*/

        // Get the unified block ID
        uint64_t block_id = Hash_Page_Address(page_address);
        //std::cout<<"TRE_Module:block_id"<<block_id<<std::endl;

        // Get current page type
        Page_Type page_type = Get_Page_Type(page_address.PageID);

        // Check if this block has been accessed before
        auto it = last_access_time_map.find(block_id);
        uint64_t idle_time = 0;

        if (it != last_access_time_map.end()) {
            idle_time = now - it->second;
            // 写后读才更新这个时间，读前未写不记录
            if (!is_write){
                last_access_time_map[block_id] = now;
            }
        }else{
            idle_time = 0;
        }

        // Update the last access time for this block
        if (is_write){
            last_access_time_map[block_id] = now;
        }
        

        // For read operations, calculate RBER and delay
        if (!is_write && idle_time >= 0) {
            rber = Calculate_RBER(idle_time, page_type, BASE_PEC);
            rber_dr = Calculate_DR_RBER(idle_time, page_type, BASE_PEC);

            // Track RBER statistics by levels
            if (rber < 0.005) rber_level = 0;
            else if (rber < 0.006) rber_level = 1;
            else if (rber < 0.008) rber_level = 2;
            else if (rber < 0.009) rber_level = 3;
            else if (rber < 0.01) rber_level = 4;
            else if (rber < 0.012) rber_level = 5;
            else if (rber <= 0.013) rber_level = 6;
            else rber_level = 7;

            // Increment the RBER level counter
            rber_level_counts[rber_level]++;
            
            // Track DR-RBER statistics
            if (rber_dr < 0.005) rber_dr_level = 0;
            else if (rber_dr < 0.006) rber_dr_level = 1;
            else if (rber_dr < 0.008) rber_dr_level = 2;
            else if (rber_dr < 0.009) rber_dr_level = 3;
            else if (rber_dr < 0.01) rber_dr_level = 4;
            else if (rber_dr < 0.012) rber_dr_level = 5;
            else if (rber_dr <= 0.013) rber_dr_level = 6;
            else rber_dr_level = 7;

            // Increment the DR-RBER level counter
            dr_rber_level_counts[rber_dr_level]++;
            
            // Calculate only RBER (RBER - DR_RBER)
            only_rber = (rber - rber_dr >= 0) ? (rber - rber_dr) : 0;

            // Track ONLY-RBER statistics
            if (only_rber < 0.005) only_rber_level = 0;
            else if (only_rber < 0.006) only_rber_level = 1;
            else if (only_rber < 0.008) only_rber_level = 2;
            else if (only_rber < 0.009) only_rber_level = 3;
            else if (only_rber < 0.01) only_rber_level = 4;
            else if (only_rber < 0.012) only_rber_level = 5;
            else if (only_rber <= 0.013) only_rber_level = 6;
            else only_rber_level = 7;

            // Increment the ONLY-RBER level counter
            only_rber_level_counts[only_rber_level]++;

            // Calculate RBER delay for RL reward
            delay_us_rber = Calculate_Delay(rber);

            // Check if RBER exceeds threshold for TRE
            if (rber >= tre_threshold) {
                delay_us_dr_rber = Calculate_Delay(rber_dr);
                
                // Ensure we don't get negative delays by using max(0, rber_delay - dr_rber_delay)
                // Also ensure dr_rber_delay isn't too large to cause timing issues
                if (delay_us_dr_rber > delay_us_rber) {
                    delay_us = 0;
                } else {
                    delay_us = delay_us_rber - delay_us_dr_rber;
                }

                if (rber > 0.013 || rber_dr > 0.013) {
                    if (rber > rber_dr) {
                        delay_us = Calculate_Delay(rber-rber_dr);
                    } else {
                        std::cout << "rber_dr > rber" << std::endl;
                    }
                }
            }
            
            // Log TRE event if logging is enabled
            if (logging_enabled) {
                Log_TRE_Event(rber, delay_us_rber, rber_dr, delay_us_dr_rber, delay_us, true);
            }
        }

        // Fill TRE_Data structure with all calculated values
        tre_data.delay_us_rber = delay_us_rber;
        tre_data.delay_us = delay_us;
        tre_data.delay_us_dr_rber = delay_us_dr_rber;
        tre_data.rber = rber;
        tre_data.rber_level = rber_level;
        tre_data.rber_dr = rber_dr;
        tre_data.rber_dr_level = rber_dr_level;
        tre_data.only_rber = only_rber;
        tre_data.only_rber_level = only_rber_level;

        // === 计算三个页面类型的RBER值（用于第一层RL奖励平均值计算） ===
        // 为了计算第一层RL的奖励平均值，需要获取当前idle时间下三个页面类型的RBER
        if (!is_write && idle_time >= 0) {
            // 计算LP页面类型的RBER
            tre_data.rber_lp = Calculate_RBER(idle_time, Page_Type::LP, BASE_PEC);

            // 计算MP页面类型的RBER
            tre_data.rber_mp = Calculate_RBER(idle_time, Page_Type::MP, BASE_PEC);

            // 计算UP页面类型的RBER
            tre_data.rber_up = Calculate_RBER(idle_time, Page_Type::UP, BASE_PEC);

            // 计算LP页面类型的TRE延时
            if(tre_data.rber_lp >= tre_threshold){
                tre_data.delay_us_rber_lp = Calculate_Delay(tre_data.rber_lp);
            }

            // 计算MP页面类型的TRE延时
            if(tre_data.rber_mp >= tre_threshold){
                tre_data.delay_us_rber_mp = Calculate_Delay(tre_data.rber_mp);
            }

            // 计算UP页面类型的TRE延时
            if(tre_data.rber_up >= tre_threshold){
                tre_data.delay_us_rber_up = Calculate_Delay(tre_data.rber_up);
            }
        } else {
            // 写操作或无效idle时间时，设置为0
            tre_data.rber_lp = 0.0;
            tre_data.rber_mp = 0.0;
            tre_data.rber_up = 0.0;
            tre_data.delay_us_rber_lp = 0.0;
            tre_data.delay_us_rber_mp = 0.0;
            tre_data.delay_us_rber_up = 0.0;
        }

        return tre_data;
    }

    void TRE_Module::Log_TRE_Event(double rber, uint64_t delay_us_rber, double rber_dr, uint64_t delay_us_dr_rber, uint64_t final_delay_us, bool is_read)
    {
        if (logging_enabled && log_file.is_open()) {
            log_file << rber << "," << delay_us_rber << "," << rber_dr << "," 
                     << delay_us_dr_rber << "," << final_delay_us << "," 
                     << (is_read ? "1" : "0") << std::endl;
        }
    }

    void TRE_Module::Print_Statistics()
    {
        if (!tre_enabled) {
            std::cout << "TRE Module is disabled - no statistics to report." << std::endl;
            return;
        }
        
        std::cout << "\n===== TRE Module Statistics =====" << std::endl;
        std::cout << "Total Read Operations: " << total_read_count << std::endl;
        std::cout << "Total Write Operations: " << total_write_count << std::endl;
        
        std::cout << "\nRBER Level Distribution:" << std::endl;
        for (int i = 0; i < 8; i++) {
            std::cout << "Level " << i << ": " << rber_level_counts[i] << std::endl;
        }
        
        std::cout << "\nDR-RBER Level Distribution:" << std::endl;
        for (int i = 0; i < 8; i++) {
            std::cout << "Level " << i << ": " << dr_rber_level_counts[i] << std::endl;
        }
        
        std::cout << "\nONLY-RBER Level Distribution:" << std::endl;
        for (int i = 0; i < 8; i++) {
            std::cout << "Level " << i << ": " << only_rber_level_counts[i] << std::endl;
        }
        std::cout << "===============================" << std::endl;
    }
} 