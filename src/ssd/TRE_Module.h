#ifndef TRE_MODULE_H
#define TRE_MODULE_H

#include <cmath>
#include <fstream>
#include <string>
#include <unordered_map>
#include <utility>
#include "../nvm_chip/flash_memory/FlashTypes.h"
#include "../nvm_chip/flash_memory/Physical_Page_Address.h"

namespace SSD_Components
{
    enum class Page_Type {
        LP, // Lower Page (LSB/SLC)
        MP, // Middle Page (CSB/MLC)
        UP  // Upper Page (MSB/TLC)
    };

    // TRE数据结构，包含所有TRE相关的返回数据
    struct TRE_Data {
        uint64_t delay_us_rber;      // RBER-based delay for RL reward calculation
        uint64_t delay_us;           // Actual TRE-induced delay
        uint64_t delay_us_dr_rber;   // DR RBER-based delay
        double rber;                 // Raw Bit Error Rate
        int rber_level;              // RBER level (0-7)
        double rber_dr;              // DR RBER
        int rber_dr_level;           // DR RBER level (0-7)
        double only_rber;            // Only RBER (without DR)
        int only_rber_level;         // Only RBER level (0-7)

        // 三个页面类型的RBER值（用于第一层RL奖励平均值计算）
        double rber_lp;              // Lower Page RBER
        double rber_mp;              // Middle Page RBER
        double rber_up;              // Upper Page RBER

        double delay_us_rber_lp;
        double delay_us_rber_mp;
        double delay_us_rber_up;

        // 构造函数
        TRE_Data() : delay_us_rber(0), delay_us(0), delay_us_dr_rber(0),
                     rber(0.0), rber_level(0), rber_dr(0.0), rber_dr_level(0),
                     only_rber(0.0), only_rber_level(0),
                     rber_lp(0.0), rber_mp(0.0), rber_up(0.0),
                     delay_us_rber_lp(0.0), delay_us_rber_mp(0.0), delay_us_rber_up(0.0) {}
    };

    class TRE_Module
    {
    public:
        TRE_Module();
        ~TRE_Module();

        // Initialize the module with the specified log file path
        void Initialize(const std::string& log_file_path);

        // Calculate RBER (Raw Bit Error Rate) based on idle time, page type, and PEC
        double Calculate_RBER(uint64_t idle_time_ns, Page_Type page_type, int pec);
        
        // Calculate DR RBER using the alternative model
        double Calculate_DR_RBER(uint64_t idle_time_ns, Page_Type page_type, int pec);

        // Calculate delay induced by RBER
        uint64_t Calculate_Delay(double rber);

        /**
         * Update the access time for a page and calculate any delay due to TRE effects
         * @param page_address The physical address of the page being accessed
         * @param is_write Whether this is a write operation (true) or a read operation (false)
         * @return TRE_Data structure containing all TRE-related data including delays, RBER values, and levels
         */
        TRE_Data Update_Page_Access(const NVM::FlashMemory::Physical_Page_Address& page_address, bool is_write);

        // Log TRE event to the output file
        void Log_TRE_Event(double rber, uint64_t delay_us_rber, double rber_dr, uint64_t delay_us_dr_rber, uint64_t final_delay_us, bool is_write);

        // Print statistics at the end
        void Print_Statistics();
        
        // Check if TRE is enabled based on configuration
        inline bool Is_TRE_Enabled() const { return tre_enabled; }
        
        // Set TRE enabled status
        inline void Set_TRE_Enabled(bool enabled) { tre_enabled = enabled; }

    private:
        // Flag to indicate if TRE functionality is enabled
        bool tre_enabled;
        
        // Base PEC value, simulating initial uneven wear
        int BASE_PEC;
        
        // TRE parameters from config
        double tre_threshold;
        double tre_solid_factor;
        
        // Statistics for read/write operations
        uint64_t total_read_count;
        uint64_t total_write_count;
        
        // Statistics for RBER distribution
        uint64_t rber_level_counts[8];
        uint64_t dr_rber_level_counts[8];
        uint64_t only_rber_level_counts[8];

        // Get page type based on page
        Page_Type Get_Page_Type(flash_page_ID_type page_id);

        // Map of physical page addresses to their last access time
        // Using unified_block_id to ensure consistency across strategies
        std::unordered_map<uint64_t, uint64_t> last_access_time_map;

        // Output log file
        std::ofstream log_file;
        bool logging_enabled;

        // Hash function for Physical_Page_Address
        uint64_t Hash_Page_Address(const NVM::FlashMemory::Physical_Page_Address& addr);
    };
}

#endif // !TRE_MODULE_H 