<html><head><style type="text/css">

          body
          {
          font-family: sans-serif;
          font-size: 90%;
          margin: 8pt 8pt 8pt 8pt;
          text-align: justify;
          background-color: White;
          }

          h1 { font-weight: bold; text-align: left;  }
          h2 { font: 140% sans-serif; font-weight: bold; text-align: left;  }
          h3 { font: 120% sans-serif; font-weight: bold; text-align: left;  }
          h4 { font: bold 100% sans-serif; font-weight: bold; text-align: left;  }
          h5 { font: italic 100% sans-serif; font-weight: bold; text-align: left;  }
          h6 { font: small-caps 100% sans-serif; font-weight: bold; text-align: left;  }

          code
          {
          font-family: &quot;Courier New&quot;, Courier, mono;
          }

          pre
          {
          border-top: gray 0.5pt solid;
          border-right: gray 0.5pt solid;
          border-left: gray 0.5pt solid;
          border-bottom: gray 0.5pt solid;
          padding-top: 2pt;
          padding-right: 2pt;
          padding-left: 2pt;
          padding-bottom: 2pt;
          display: block;
          font-family: &quot;courier new&quot;, courier, mono;
          background-color: #eeeeee;
          }

          a
          {
          color: #000080;
          text-decoration: none;
          }

          a:hover
          {
          text-decoration: underline;
          }

          .reference-header
          {
          border-top: gray 0.5pt solid;
          border-right: gray 0.5pt solid;
          border-left: gray 0.5pt solid;
          border-bottom: gray 0.5pt solid;
          padding-top: 2pt;
          padding-right: 2pt;
          padding-left: 2pt;
          padding-bottom: 2pt;
          background-color: #dedede;
          }

          .parameter-name
          {
          font-style: italic;
          }

          .indented
          {
          margin-left: 0.5cm;
          }

          a.toc1
          {
          margin-left: 0.0cm;
          }

          a.toc2
          {
          margin-left: 0.75cm;
          }

          a.toc3
          {
          margin-left: 1.5cm;
          }

        </style></head><body><h1>RAPIDXML Manual</h1><h3>Version 1.13</h3><detaileddescription xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><para><i>Copyright (C) 2006, 2009 Marcin Kalicinski</i><br/><i>See accompanying file <a href="license.txt">license.txt</a> for license information.</i><hr/><h2 level="2">Table of Contents</h2></para><para><toc><toc-contents><a href="#namespacerapidxml_1what_is_rapidxml" class="toc1">1. What is RapidXml?</a><br/><a href="#namespacerapidxml_1dependencies_and_compatibility" class="toc2">1.1 Dependencies And Compatibility</a><br/><a href="#namespacerapidxml_1character_types_and_encodings" class="toc2">1.2 Character Types And Encodings</a><br/><a href="#namespacerapidxml_1error_handling" class="toc2">1.3 Error Handling</a><br/><a href="#namespacerapidxml_1memory_allocation" class="toc2">1.4 Memory Allocation</a><br/><a href="#namespacerapidxml_1w3c_compliance" class="toc2">1.5 W3C Compliance</a><br/><a href="#namespacerapidxml_1api_design" class="toc2">1.6 API Design</a><br/><a href="#namespacerapidxml_1reliability" class="toc2">1.7 Reliability</a><br/><a href="#namespacerapidxml_1acknowledgements" class="toc2">1.8 Acknowledgements</a><br/><a href="#namespacerapidxml_1two_minute_tutorial" class="toc1">2. Two Minute Tutorial</a><br/><a href="#namespacerapidxml_1parsing" class="toc2">2.1 Parsing</a><br/><a href="#namespacerapidxml_1accessing_dom_tree" class="toc2">2.2 Accessing The DOM Tree</a><br/><a href="#namespacerapidxml_1modifying_dom_tree" class="toc2">2.3 Modifying The DOM Tree</a><br/><a href="#namespacerapidxml_1printing" class="toc2">2.4 Printing XML</a><br/><a href="#namespacerapidxml_1differences" class="toc1">3. Differences From Regular XML Parsers</a><br/><a href="#namespacerapidxml_1lifetime_of_source_text" class="toc2">3.1 Lifetime Of Source Text</a><br/><a href="#namespacerapidxml_1ownership_of_strings" class="toc2">3.2 Ownership Of Strings</a><br/><a href="#namespacerapidxml_1destructive_non_destructive" class="toc2">3.3 Destructive Vs Non-Destructive Mode</a><br/><a href="#namespacerapidxml_1performance" class="toc1">4. Performance</a><br/><a href="#namespacerapidxml_1performance_charts" class="toc2">4.1 Comparison With Other Parsers</a><br/><a href="#namespacerapidxml_1reference" class="toc1">5. Reference</a><br/></toc-contents></toc><br/></para><sect1><h2 id="namespacerapidxml_1what_is_rapidxml">1. What is RapidXml?</h2><para><a href="http://rapidxml.sourceforge.net">RapidXml</a> is an attempt to create the fastest XML DOM parser possible, while retaining useability, portability and reasonable W3C compatibility. It is an in-situ parser written in C++, with parsing speed approaching that of <code>strlen()</code> function executed on the same data. <br/><br/>
 Entire parser is contained in a single header file, so no building or linking is neccesary. To use it you just need to copy <code>rapidxml.hpp</code> file to a convenient place (such as your project directory), and include it where needed. You may also want to use printing functions contained in header <code>rapidxml_print.hpp</code>.</para><sect2><h3 id="namespacerapidxml_1dependencies_and_compatibility">1.1 Dependencies And Compatibility</h3><para>RapidXml has <i>no dependencies</i> other than a very small subset of standard C++ library (<code>&lt;cassert&gt;</code>, <code>&lt;cstdlib&gt;</code>, <code>&lt;new&gt;</code> and <code>&lt;exception&gt;</code>, unless exceptions are disabled). It should compile on any reasonably conformant compiler, and was tested on Visual C++ 2003, Visual C++ 2005, Visual C++ 2008, gcc 3, gcc 4, and Comeau 4.3.3. Care was taken that no warnings are produced on these compilers, even with highest warning levels enabled.</para></sect2><sect2><h3 id="namespacerapidxml_1character_types_and_encodings">1.2 Character Types And Encodings</h3><para>RapidXml is character type agnostic, and can work both with narrow and wide characters. Current version does not fully support UTF-16 or UTF-32, so use of wide characters is somewhat incapacitated. However, it should succesfully parse <code>wchar_t</code> strings containing UTF-16 or UTF-32 if endianness of the data matches that of the machine. UTF-8 is fully supported, including all numeric character references, which are expanded into appropriate UTF-8 byte sequences (unless you enable parse_no_utf8 flag). <br/><br/>
 Note that RapidXml performs no decoding - strings returned by name() and value() functions will contain text encoded using the same encoding as source file. Rapidxml understands and expands the following character references: <code>&amp;apos; &amp;amp; &amp;quot; &amp;lt; &amp;gt; &amp;#...;</code> Other character references are not expanded.</para></sect2><sect2><h3 id="namespacerapidxml_1error_handling">1.3 Error Handling</h3><para>By default, RapidXml uses C++ exceptions to report errors. If this behaviour is undesirable, RAPIDXML_NO_EXCEPTIONS can be defined to suppress exception code. See <a href="#classrapidxml_1_1parse__error" kindref="compound">parse_error</a> class and <a href="#namespacerapidxml_ff5d67f74437199d316d2b2660653ae1_1ff5d67f74437199d316d2b2660653ae1" kindref="member">parse_error_handler()</a> function for more information.</para></sect2><sect2><h3 id="namespacerapidxml_1memory_allocation">1.4 Memory Allocation</h3><para>RapidXml uses a special memory pool object to allocate nodes and attributes, because direct allocation using <code>new</code> operator would be far too slow. Underlying memory allocations performed by the pool can be customized by use of <a href="#classrapidxml_1_1memory__pool_c0a55a6ef0837dca67572e357100d78a_1c0a55a6ef0837dca67572e357100d78a" kindref="member">memory_pool::set_allocator()</a> function. See class <a href="#classrapidxml_1_1memory__pool" kindref="compound">memory_pool</a> for more information.</para></sect2><sect2><h3 id="namespacerapidxml_1w3c_compliance">1.5 W3C Compliance</h3><para>RapidXml is not a W3C compliant parser, primarily because it ignores DOCTYPE declarations. There is a number of other, minor incompatibilities as well. Still, it can successfully parse and produce complete trees of all valid XML files in W3C conformance suite (over 1000 files specially designed to find flaws in XML processors). In destructive mode it performs whitespace normalization and character entity substitution for a small set of built-in entities.</para></sect2><sect2><h3 id="namespacerapidxml_1api_design">1.6 API Design</h3><para>RapidXml API is minimalistic, to reduce code size as much as possible, and facilitate use in embedded environments. Additional convenience functions are provided in separate headers: <code>rapidxml_utils.hpp</code> and <code><a href="#rapidxml__print_8hpp" kindref="compound">rapidxml_print.hpp</a></code>. Contents of these headers is not an essential part of the library, and is currently not documented (otherwise than with comments in code).</para></sect2><sect2><h3 id="namespacerapidxml_1reliability">1.7 Reliability</h3><para>RapidXml is very robust and comes with a large harness of unit tests. Special care has been taken to ensure stability of the parser no matter what source text is thrown at it. One of the unit tests produces 100,000 randomly corrupted variants of XML document, which (when uncorrupted) contains all constructs recognized by RapidXml. RapidXml passes this test when it correctly recognizes that errors have been introduced, and does not crash or loop indefinitely. <br/><br/>
 Another unit test puts RapidXml head-to-head with another, well estabilished XML parser, and verifies that their outputs match across a wide variety of small and large documents. <br/><br/>
 Yet another test feeds RapidXml with over 1000 test files from W3C compliance suite, and verifies that correct results are obtained. There are also additional tests that verify each API function separately, and test that various parsing modes work as expected.</para></sect2><sect2><h3 id="namespacerapidxml_1acknowledgements">1.8 Acknowledgements</h3><para>I would like to thank Arseny Kapoulkine for his work on <a href="http://code.google.com/p/pugixml">pugixml</a>, which was an inspiration for this project. Additional thanks go to Kristen Wegner for creating <a href="http://www.codeproject.com/soap/pugxml.asp">pugxml</a>, from which pugixml was derived. Janusz Wohlfeil kindly ran RapidXml speed tests on hardware that I did not have access to, allowing me to expand performance comparison table.</para></sect2></sect1><sect1><h2 id="namespacerapidxml_1two_minute_tutorial">2. Two Minute Tutorial</h2><sect2><h3 id="namespacerapidxml_1parsing">2.1 Parsing</h3><para>The following code causes RapidXml to parse a zero-terminated string named <code>text</code>: <pre>using namespace rapidxml;
xml_document&lt;&gt; doc;    // character type defaults to char
doc.parse&lt;0&gt;(text);    // 0 means default parse flags
</pre><code>doc</code> object is now a root of DOM tree containing representation of the parsed XML. Because all RapidXml interface is contained inside namespace <code>rapidxml</code>, users must either bring contents of this namespace into scope, or fully qualify all the names. Class <a href="#classrapidxml_1_1xml__document" kindref="compound">xml_document</a> represents a root of the DOM hierarchy. By means of public inheritance, it is also an <a href="#classrapidxml_1_1xml__node" kindref="compound">xml_node</a> and a <a href="#classrapidxml_1_1memory__pool" kindref="compound">memory_pool</a>. Template parameter of <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function is used to specify parsing flags, with which you can fine-tune behaviour of the parser. Note that flags must be a compile-time constant.</para></sect2><sect2><h3 id="namespacerapidxml_1accessing_dom_tree">2.2 Accessing The DOM Tree</h3><para>To access the DOM tree, use methods of <a href="#classrapidxml_1_1xml__node" kindref="compound">xml_node</a> and <a href="#classrapidxml_1_1xml__attribute" kindref="compound">xml_attribute</a> classes: <pre>cout &lt;&lt; &quot;Name of my first node is: &quot; &lt;&lt; doc.first_node()-&gt;name() &lt;&lt; &quot;\n&quot;;
xml_node&lt;&gt; *node = doc.first_node(&quot;foobar&quot;);
cout &lt;&lt; &quot;Node foobar has value &quot; &lt;&lt; node-&gt;value() &lt;&lt; &quot;\n&quot;;
for (xml_attribute&lt;&gt; *attr = node-&gt;first_attribute();
     attr; attr = attr-&gt;next_attribute())
{
    cout &lt;&lt; &quot;Node foobar has attribute &quot; &lt;&lt; attr-&gt;name() &lt;&lt; &quot; &quot;;
    cout &lt;&lt; &quot;with value &quot; &lt;&lt; attr-&gt;value() &lt;&lt; &quot;\n&quot;;
}
</pre></para></sect2><sect2><h3 id="namespacerapidxml_1modifying_dom_tree">2.3 Modifying The DOM Tree</h3><para>DOM tree produced by the parser is fully modifiable. Nodes and attributes can be added/removed, and their contents changed. The below example creates a HTML document, whose sole contents is a link to google.com website: <pre>xml_document&lt;&gt; doc;
xml_node&lt;&gt; *node = doc.allocate_node(node_element, &quot;a&quot;, &quot;Google&quot;);
doc.append_node(node);
xml_attribute&lt;&gt; *attr = doc.allocate_attribute(&quot;href&quot;, &quot;google.com&quot;);
node-&gt;append_attribute(attr);
</pre> One quirk is that nodes and attributes <i>do not own</i> the text of their names and values. This is because normally they only store pointers to the source text. So, when assigning a new name or value to the node, care must be taken to ensure proper lifetime of the string. The easiest way to achieve it is to allocate the string from the <a href="#classrapidxml_1_1xml__document" kindref="compound">xml_document</a> memory pool. In the above example this is not necessary, because we are only assigning character constants. But the code below uses <a href="#classrapidxml_1_1memory__pool_69729185bc59b0875192d667c47b8859_169729185bc59b0875192d667c47b8859" kindref="member">memory_pool::allocate_string()</a> function to allocate node name (which will have the same lifetime as the document), and assigns it to a new node: <pre>xml_document&lt;&gt; doc;
char *node_name = doc.allocate_string(name);        // Allocate string and copy name into it
xml_node&lt;&gt; *node = doc.allocate_node(node_element, node_name);  // Set node name to node_name
</pre> Check <a href="#namespacerapidxml_1reference" kindref="member">Reference</a>  section for description of the entire interface.</para></sect2><sect2><h3 id="namespacerapidxml_1printing">2.4 Printing XML</h3><para>You can print <code><a href="#classrapidxml_1_1xml__document" kindref="compound">xml_document</a></code> and <code><a href="#classrapidxml_1_1xml__node" kindref="compound">xml_node</a></code> objects into an XML string. Use <a href="#namespacerapidxml_b94d570fc4c4ab2423813cd0243326b1_1b94d570fc4c4ab2423813cd0243326b1" kindref="member">print()</a> function or operator &lt;&lt;, which are defined in <code><a href="#rapidxml__print_8hpp" kindref="compound">rapidxml_print.hpp</a></code> header. <pre>using namespace rapidxml;
xml_document&lt;&gt; doc;    // character type defaults to char
// ... some code to fill the document

// Print to stream using operator &lt;&lt;
std::cout &lt;&lt; doc;   

// Print to stream using print function, specifying printing flags
print(std::cout, doc, 0);   // 0 means default printing flags

// Print to string using output iterator
std::string s;
print(std::back_inserter(s), doc, 0);

// Print to memory buffer using output iterator
char buffer[4096];                      // You are responsible for making the buffer large enough!
char *end = print(buffer, doc, 0);      // end contains pointer to character after last printed character
*end = 0;                               // Add string terminator after XML
</pre></para></sect2></sect1><sect1><h2 id="namespacerapidxml_1differences">3. Differences From Regular XML Parsers</h2><para>RapidXml is an <i>in-situ parser</i>, which allows it to achieve very high parsing speed. In-situ means that parser does not make copies of strings. Instead, it places pointers to the <i>source text</i> in the DOM hierarchy.</para><sect2><h3 id="namespacerapidxml_1lifetime_of_source_text">3.1 Lifetime Of Source Text</h3><para>In-situ parsing requires that source text lives at least as long as the document object. If source text is destroyed, names and values of nodes in DOM tree will become destroyed as well. Additionally, whitespace processing, character entity translation, and zero-termination of strings require that source text be modified during parsing (but see non-destructive mode). This makes the text useless for further processing once it was parsed by RapidXml. <br/><br/>
 In many cases however, these are not serious issues.</para></sect2><sect2><h3 id="namespacerapidxml_1ownership_of_strings">3.2 Ownership Of Strings</h3><para>Nodes and attributes produced by RapidXml do not own their name and value strings. They merely hold the pointers to them. This means you have to be careful when setting these values manually, by using <a href="#classrapidxml_1_1xml__base_e099c291e104a0d277307fe71f5e0f9e_1e099c291e104a0d277307fe71f5e0f9e" kindref="member">xml_base::name(const Ch *)</a> or <a href="#classrapidxml_1_1xml__base_18c7469acdca771de9b4f3054053029c_118c7469acdca771de9b4f3054053029c" kindref="member">xml_base::value(const Ch *)</a> functions. Care must be taken to ensure that lifetime of the string passed is at least as long as lifetime of the node/attribute. The easiest way to achieve it is to allocate the string from <a href="#classrapidxml_1_1memory__pool" kindref="compound">memory_pool</a> owned by the document. Use <a href="#classrapidxml_1_1memory__pool_69729185bc59b0875192d667c47b8859_169729185bc59b0875192d667c47b8859" kindref="member">memory_pool::allocate_string()</a> function for this purpose.</para></sect2><sect2><h3 id="namespacerapidxml_1destructive_non_destructive">3.3 Destructive Vs Non-Destructive Mode</h3><para>By default, the parser modifies source text during the parsing process. This is required to achieve character entity translation, whitespace normalization, and zero-termination of strings. <br/><br/>
 In some cases this behaviour may be undesirable, for example if source text resides in read only memory, or is mapped to memory directly from file. By using appropriate parser flags (parse_non_destructive), source text modifications can be disabled. However, because RapidXml does in-situ parsing, it obviously has the following side-effects:<ul><li><para>no whitespace normalization is done</para></li><li><para>no entity reference translation is done</para></li><li><para>names and values are not zero-terminated, you must use <a href="#classrapidxml_1_1xml__base_0dae694c8f7e4d89f1003e2f3a15a43c_10dae694c8f7e4d89f1003e2f3a15a43c" kindref="member">xml_base::name_size()</a> and <a href="#classrapidxml_1_1xml__base_aed5ae791b7164c1ee5e649198cbb3db_1aed5ae791b7164c1ee5e649198cbb3db" kindref="member">xml_base::value_size()</a> functions to tell where they end</para></li></ul></para></sect2></sect1><sect1><h2 id="namespacerapidxml_1performance">4. Performance</h2><para>RapidXml achieves its speed through use of several techniques:<ul><li><para>In-situ parsing. When building DOM tree, RapidXml does not make copies of string data, such as node names and values. Instead, it stores pointers to interior of the source text.</para></li><li><para>Use of template metaprogramming techniques. This allows it to move much of the work to compile time. Through magic of the templates, C++ compiler generates a separate copy of parsing code for any combination of parser flags you use. In each copy, all possible decisions are made at compile time and all unused code is omitted.</para></li><li><para>Extensive use of lookup tables for parsing.</para></li><li><para>Hand-tuned C++ with profiling done on several most popular CPUs.</para></li></ul></para><para>This results in a very small and fast code: a parser which is custom tailored to exact needs with each invocation.</para><sect2><h3 id="namespacerapidxml_1performance_charts">4.1 Comparison With Other Parsers</h3><para>The table below compares speed of RapidXml to some other parsers, and to <code>strlen()</code> function executed on the same data. On a modern CPU (as of 2007), you can expect parsing throughput to be close to 1 GB/s. As a rule of thumb, parsing speed is about 50-100x faster than Xerces DOM, 30-60x faster than TinyXml, 3-12x faster than pugxml, and about 5% - 30% faster than pugixml, the fastest XML parser I know of.</para><para><ul><li><para>The test file is a real-world, 50kB large, moderately dense XML file. </para></li><li><para>All timing is done by using RDTSC instruction present in Pentium-compatible CPUs. </para></li><li><para>No profile-guided optimizations are used. </para></li><li><para>All parsers are running in their fastest modes. </para></li><li><para>The results are given in CPU cycles per character, so frequency of CPUs is irrelevant. </para></li><li><para>The results are minimum values from a large number of runs, to minimize effects of operating system activity, task switching, interrupt handling etc. </para></li><li><para>A single parse of the test file takes about 1/10th of a millisecond, so with large number of runs there is a good chance of hitting at least one no-interrupt streak, and obtaining undisturbed results. </para></li></ul><table rows="9" cols="7" border="1" cellpadding="3pt"><tr><th thead="yes"><para><center>Platform</center></para></th><th thead="yes"><para><center>Compiler</center></para></th><th thead="yes"><para>strlen() </para></th><th thead="yes"><para>RapidXml </para></th><th thead="yes"><para>pugixml 0.3 </para></th><th thead="yes"><para>pugxml </para></th><th thead="yes"><para>TinyXml  </para></th></tr><tr><td thead="no"><para><center>Pentium 4</center></para></td><td thead="no"><para><center>MSVC 8.0</center></para></td><td thead="no"><para><center>2.5</center></para></td><td thead="no"><para><center>5.4</center></para></td><td thead="no"><para><center>7.0</center></para></td><td thead="no"><para><center>61.7</center></para></td><td thead="no"><para><center>298.8</center></para></td></tr><tr><td thead="no"><para><center>Pentium 4</center></para></td><td thead="no"><para><center>gcc 4.1.1</center></para></td><td thead="no"><para><center>0.8</center></para></td><td thead="no"><para><center>6.1</center></para></td><td thead="no"><para><center>9.5</center></para></td><td thead="no"><para><center>67.0</center></para></td><td thead="no"><para><center>413.2</center></para></td></tr><tr><td thead="no"><para><center>Core 2</center></para></td><td thead="no"><para><center>MSVC 8.0</center></para></td><td thead="no"><para><center>1.0</center></para></td><td thead="no"><para><center>4.5</center></para></td><td thead="no"><para><center>5.0</center></para></td><td thead="no"><para><center>24.6</center></para></td><td thead="no"><para><center>154.8</center></para></td></tr><tr><td thead="no"><para><center>Core 2</center></para></td><td thead="no"><para><center>gcc 4.1.1</center></para></td><td thead="no"><para><center>0.6</center></para></td><td thead="no"><para><center>4.6</center></para></td><td thead="no"><para><center>5.4</center></para></td><td thead="no"><para><center>28.3</center></para></td><td thead="no"><para><center>229.3</center></para></td></tr><tr><td thead="no"><para><center>Athlon XP</center></para></td><td thead="no"><para><center>MSVC 8.0</center></para></td><td thead="no"><para><center>3.1</center></para></td><td thead="no"><para><center>7.7</center></para></td><td thead="no"><para><center>8.0</center></para></td><td thead="no"><para><center>25.5</center></para></td><td thead="no"><para><center>182.6</center></para></td></tr><tr><td thead="no"><para><center>Athlon XP</center></para></td><td thead="no"><para><center>gcc 4.1.1</center></para></td><td thead="no"><para><center>0.9</center></para></td><td thead="no"><para><center>8.2</center></para></td><td thead="no"><para><center>9.2</center></para></td><td thead="no"><para><center>33.7</center></para></td><td thead="no"><para><center>265.2</center></para></td></tr><tr><td thead="no"><para><center>Pentium 3</center></para></td><td thead="no"><para><center>MSVC 8.0</center></para></td><td thead="no"><para><center>2.0</center></para></td><td thead="no"><para><center>6.3</center></para></td><td thead="no"><para><center>7.0</center></para></td><td thead="no"><para><center>30.9</center></para></td><td thead="no"><para><center>211.9</center></para></td></tr><tr><td thead="no"><para><center>Pentium 3</center></para></td><td thead="no"><para><center>gcc 4.1.1</center></para></td><td thead="no"><para><center>1.0</center></para></td><td thead="no"><para><center>6.7</center></para></td><td thead="no"><para><center>8.9</center></para></td><td thead="no"><para><center>35.3</center></para></td><td thead="no"><para><center>316.0</center></para></td></tr></table><i>(*) All results are in CPU cycles per character of source text</i></para></sect2></sect1><sect1><h2 id="namespacerapidxml_1reference">5. Reference</h2><para>This section lists all classes, functions, constants etc. and describes them in detail. </para></sect1></detaileddescription><dl><dt>class
								  template
							   <a href="#classrapidxml_1_1memory__pool">rapidxml::memory_pool</a></dt><dt class="indented">
				constructor
			 <a href="#classrapidxml_1_1memory__pool_f8fb3c8f1a564f8045c40bcd07a89866_1f8fb3c8f1a564f8045c40bcd07a89866">memory_pool()</a></dt><dt class="indented">
				destructor
			 <a href="#classrapidxml_1_1memory__pool_6f8c7990d9ec1ed2acf6558b238570eb_16f8c7990d9ec1ed2acf6558b238570eb">~memory_pool()</a></dt><dt class="indented">function <a href="#classrapidxml_1_1memory__pool_750ba3c610b129ac057d817509d08f41_1750ba3c610b129ac057d817509d08f41">allocate_node(node_type type, const Ch *name=0, const Ch *value=0, std::size_t name_size=0, std::size_t value_size=0)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1memory__pool_462de142669e0ff649e8e615b82bf457_1462de142669e0ff649e8e615b82bf457">allocate_attribute(const Ch *name=0, const Ch *value=0, std::size_t name_size=0, std::size_t value_size=0)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1memory__pool_69729185bc59b0875192d667c47b8859_169729185bc59b0875192d667c47b8859">allocate_string(const Ch *source=0, std::size_t size=0)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1memory__pool_95c49fcb056e9103ec906a59e3e01d76_195c49fcb056e9103ec906a59e3e01d76">clone_node(const xml_node&lt; Ch &gt; *source, xml_node&lt; Ch &gt; *result=0)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1memory__pool_c8bb3912a3ce86b15842e79d0b421204_1c8bb3912a3ce86b15842e79d0b421204">clear()</a></dt><dt class="indented">function <a href="#classrapidxml_1_1memory__pool_c0a55a6ef0837dca67572e357100d78a_1c0a55a6ef0837dca67572e357100d78a">set_allocator(alloc_func *af, free_func *ff)</a></dt><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><p/><p/><dt>class <a href="#classrapidxml_1_1parse__error">rapidxml::parse_error</a></dt><dt class="indented">
				constructor
			 <a href="#classrapidxml_1_1parse__error_4dd8d1bdbd9221df4dcb90cafaee3332_14dd8d1bdbd9221df4dcb90cafaee3332">parse_error(const char *what, void *where)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1parse__error_ff06f49065b54a8a86e02e9a2441a8ba_1ff06f49065b54a8a86e02e9a2441a8ba">what() const </a></dt><dt class="indented">function <a href="#classrapidxml_1_1parse__error_377be7d201d95221c318682c35377aca_1377be7d201d95221c318682c35377aca">where() const </a></dt><dt class="indented"/><dt class="indented"/><p/><dt>class
								  template
							   <a href="#classrapidxml_1_1xml__attribute">rapidxml::xml_attribute</a></dt><dt class="indented">
				constructor
			 <a href="#classrapidxml_1_1xml__attribute_d5464aadf08269a886b730993525db34_1d5464aadf08269a886b730993525db34">xml_attribute()</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__attribute_77aea7d8d996ba4f6bd61cc478a4e72d_177aea7d8d996ba4f6bd61cc478a4e72d">document() const </a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__attribute_5c4a98d2b75f9b41b12c110108fd55ab_15c4a98d2b75f9b41b12c110108fd55ab">previous_attribute(const Ch *name=0, std::size_t name_size=0, bool case_sensitive=true) const </a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__attribute_1b8a814d0d3a7165396b08433eee8a91_11b8a814d0d3a7165396b08433eee8a91">next_attribute(const Ch *name=0, std::size_t name_size=0, bool case_sensitive=true) const </a></dt><dt class="indented"/><dt class="indented"/><dt class="indented"/><p/><dt>class
								  template
							   <a href="#classrapidxml_1_1xml__base">rapidxml::xml_base</a></dt><dt class="indented">
				constructor
			 <a href="#classrapidxml_1_1xml__base_23630d2c130a9e0e3f3afa7584a9b218_123630d2c130a9e0e3f3afa7584a9b218">xml_base()</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__base_622eade29fdf7806d3ef93ac4d90e707_1622eade29fdf7806d3ef93ac4d90e707">name() const </a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__base_0dae694c8f7e4d89f1003e2f3a15a43c_10dae694c8f7e4d89f1003e2f3a15a43c">name_size() const </a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__base_c54fa4987fb503916a7b541eb15c9c7f_1c54fa4987fb503916a7b541eb15c9c7f">value() const </a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__base_aed5ae791b7164c1ee5e649198cbb3db_1aed5ae791b7164c1ee5e649198cbb3db">value_size() const </a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__base_4e7e23d06d48126c65b1f6266acfba5c_14e7e23d06d48126c65b1f6266acfba5c">name(const Ch *name, std::size_t size)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__base_e099c291e104a0d277307fe71f5e0f9e_1e099c291e104a0d277307fe71f5e0f9e">name(const Ch *name)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__base_d9640aa3f5374673cb72a5289b6c91eb_1d9640aa3f5374673cb72a5289b6c91eb">value(const Ch *value, std::size_t size)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__base_18c7469acdca771de9b4f3054053029c_118c7469acdca771de9b4f3054053029c">value(const Ch *value)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__base_798e8df7ea53ade4d9f0701017dce80e_1798e8df7ea53ade4d9f0701017dce80e">parent() const </a></dt><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><p/><dt>class
								  template
							   <a href="#classrapidxml_1_1xml__document">rapidxml::xml_document</a></dt><dt class="indented">
				constructor
			 <a href="#classrapidxml_1_1xml__document_6ce266cc52d549c42abe3a3d5e8af9ba_16ce266cc52d549c42abe3a3d5e8af9ba">xml_document()</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c">parse(Ch *text)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__document_c8bb3912a3ce86b15842e79d0b421204_1c8bb3912a3ce86b15842e79d0b421204">clear()</a></dt><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><p/><p/><p/><p/><p/><p/><p/><p/><p/><dt>class
								  template
							   <a href="#classrapidxml_1_1xml__node">rapidxml::xml_node</a></dt><dt class="indented">
				constructor
			 <a href="#classrapidxml_1_1xml__node_34c55af3504549a475e5b9dfcaa6adf5_134c55af3504549a475e5b9dfcaa6adf5">xml_node(node_type type)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_975e86937621ae4afe6a423219de30d0_1975e86937621ae4afe6a423219de30d0">type() const </a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_77aea7d8d996ba4f6bd61cc478a4e72d_177aea7d8d996ba4f6bd61cc478a4e72d">document() const </a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_7823e36687669e59c2afdf66334ef35a_17823e36687669e59c2afdf66334ef35a">first_node(const Ch *name=0, std::size_t name_size=0, bool case_sensitive=true) const </a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_fcb6e2209b591a36d2dadba20d2bc7cc_1fcb6e2209b591a36d2dadba20d2bc7cc">last_node(const Ch *name=0, std::size_t name_size=0, bool case_sensitive=true) const </a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_ac2f6886c0107e9d5f156e9542546df6_1ac2f6886c0107e9d5f156e9542546df6">previous_sibling(const Ch *name=0, std::size_t name_size=0, bool case_sensitive=true) const </a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_b3ead2cefecc03a813836203e3f6f38f_1b3ead2cefecc03a813836203e3f6f38f">next_sibling(const Ch *name=0, std::size_t name_size=0, bool case_sensitive=true) const </a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_5810a09f82f8d53efbe9456286dcec83_15810a09f82f8d53efbe9456286dcec83">first_attribute(const Ch *name=0, std::size_t name_size=0, bool case_sensitive=true) const </a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_16953d66751b5b949ee4ee2d9c0bc63a_116953d66751b5b949ee4ee2d9c0bc63a">last_attribute(const Ch *name=0, std::size_t name_size=0, bool case_sensitive=true) const </a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_a78759bfa429fa2ab6bc5fe617cfa3cf_1a78759bfa429fa2ab6bc5fe617cfa3cf">type(node_type type)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_0c39df6617e709eb2fba11300dea63f2_10c39df6617e709eb2fba11300dea63f2">prepend_node(xml_node&lt; Ch &gt; *child)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_86de2e22276826089b7baed2599f8dee_186de2e22276826089b7baed2599f8dee">append_node(xml_node&lt; Ch &gt; *child)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_780972a57fc447250ab47cc8f421b65e_1780972a57fc447250ab47cc8f421b65e">insert_node(xml_node&lt; Ch &gt; *where, xml_node&lt; Ch &gt; *child)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_9a31d861e1bddc710839c551a5d2b3a4_19a31d861e1bddc710839c551a5d2b3a4">remove_first_node()</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_87addf2bc127ee31aa4b5295d3c9b530_187addf2bc127ee31aa4b5295d3c9b530">remove_last_node()</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_9316463a2201631e7e2062b17729f9cd_19316463a2201631e7e2062b17729f9cd">remove_node(xml_node&lt; Ch &gt; *where)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_0218147d13e41d5fa60ced4e7a7e9726_10218147d13e41d5fa60ced4e7a7e9726">remove_all_nodes()</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_f6dffa513da74cc0be71a7ba84f8265e_1f6dffa513da74cc0be71a7ba84f8265e">prepend_attribute(xml_attribute&lt; Ch &gt; *attribute)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_8fbd4f5ef7169d493da9f8d87ac04b77_18fbd4f5ef7169d493da9f8d87ac04b77">append_attribute(xml_attribute&lt; Ch &gt; *attribute)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_070d5888b0557fe06a5b24961de1b988_1070d5888b0557fe06a5b24961de1b988">insert_attribute(xml_attribute&lt; Ch &gt; *where, xml_attribute&lt; Ch &gt; *attribute)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_4eea4a7f6cb484ca9944f7eafe6e1843_14eea4a7f6cb484ca9944f7eafe6e1843">remove_first_attribute()</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_37d87c4d5d89fa0cf05b72ee8d4cba3b_137d87c4d5d89fa0cf05b72ee8d4cba3b">remove_last_attribute()</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_c75154db2e768c0e5b541fc8cd0775ab_1c75154db2e768c0e5b541fc8cd0775ab">remove_attribute(xml_attribute&lt; Ch &gt; *where)</a></dt><dt class="indented">function <a href="#classrapidxml_1_1xml__node_59e6ad4cfd5e8096c052e71d79561eda_159e6ad4cfd5e8096c052e71d79561eda">remove_all_attributes()</a></dt><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><p/><dt>namespace <a href="#namespacerapidxml">rapidxml</a></dt><dt class="indented">enum <a href="#namespacerapidxml_6a276b85e2da28c5f9c3dbce61c55682_16a276b85e2da28c5f9c3dbce61c55682">node_type</a></dt><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented"/><dt class="indented">function <a href="#namespacerapidxml_ff5d67f74437199d316d2b2660653ae1_1ff5d67f74437199d316d2b2660653ae1">parse_error_handler(const char *what, void *where)</a></dt><dt class="indented">function <a href="#namespacerapidxml_b94d570fc4c4ab2423813cd0243326b1_1b94d570fc4c4ab2423813cd0243326b1">print(OutIt out, const xml_node&lt; Ch &gt; &amp;node, int flags=0)</a></dt><dt class="indented">function <a href="#namespacerapidxml_13bc37d6d1047acb0efdbc1689221a5e_113bc37d6d1047acb0efdbc1689221a5e">print(std::basic_ostream&lt; Ch &gt; &amp;out, const xml_node&lt; Ch &gt; &amp;node, int flags=0)</a></dt><dt class="indented">function <a href="#namespacerapidxml_5619b38000d967fb223b2b0a8c17463a_15619b38000d967fb223b2b0a8c17463a">operator&lt;&lt;(std::basic_ostream&lt; Ch &gt; &amp;out, const xml_node&lt; Ch &gt; &amp;node)</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_87e8bbab53702cf3b438bd553c10b6b9_187e8bbab53702cf3b438bd553c10b6b9">parse_no_data_nodes</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_97e2c4fdc04fae17126f9971a4fc993e_197e2c4fdc04fae17126f9971a4fc993e">parse_no_element_values</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_9cae3801e70437cbc410c24bf6be691c_19cae3801e70437cbc410c24bf6be691c">parse_no_string_terminators</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_7223b7815c4fb8b42e6e4e77e1ea6b97_17223b7815c4fb8b42e6e4e77e1ea6b97">parse_no_entity_translation</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_ccde57f6054857ee4042a1b4d98c83b9_1ccde57f6054857ee4042a1b4d98c83b9">parse_no_utf8</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_52e2c934ad9c845a5f4cc49570470556_152e2c934ad9c845a5f4cc49570470556">parse_declaration_node</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_0f7479dacbc868456d07897a8c072784_10f7479dacbc868456d07897a8c072784">parse_comment_nodes</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_8e187746ba1ca04f107951ad32df962e_18e187746ba1ca04f107951ad32df962e">parse_doctype_node</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_1c20b2b2b75711cd76423e119c49f830_11c20b2b2b75711cd76423e119c49f830">parse_pi_nodes</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_a5daff9d61c7d4eaf98e4d42efe628ee_1a5daff9d61c7d4eaf98e4d42efe628ee">parse_validate_closing_tags</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_ac1f06b1afd47b812732fb521b146fd9_1ac1f06b1afd47b812732fb521b146fd9">parse_trim_whitespace</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_88f95d4e275ba01408fefde83078651b_188f95d4e275ba01408fefde83078651b">parse_normalize_whitespace</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_45751cf2f38fd6915f35b3122b46d5b6_145751cf2f38fd6915f35b3122b46d5b6">parse_default</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_a97ba1a0a79a6d66f4eef3612508d943_1a97ba1a0a79a6d66f4eef3612508d943">parse_non_destructive</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_398c5476e76102f8bd76c10bb0abbe10_1398c5476e76102f8bd76c10bb0abbe10">parse_fastest</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_b4f2515265facb42291570307924bd57_1b4f2515265facb42291570307924bd57">parse_full</a></dt><dt class="indented">
				constant
			 <a href="#namespacerapidxml_b08b8d4293c203b69ed6c5ae77ac1907_1b08b8d4293c203b69ed6c5ae77ac1907">print_no_indenting</a></dt><p/><p/><p/><p/></dl><hr/><h3 class="reference-header" id="classrapidxml_1_1memory__pool">class
									  template
								   rapidxml::memory_pool</h3>

							  Defined in <a href="rapidxml.hpp">rapidxml.hpp</a><br/>
								  Base class for
								  <a href="#classrapidxml_1_1xml__document">xml_document</a> <h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">This class is used by the parser to create new nodes and attributes, without overheads of dynamic memory allocation. In most cases, you will not need to use this class directly. However, if you need to create nodes manually or modify names/values of nodes, you are encouraged to use <a href="#classrapidxml_1_1memory__pool" kindref="compound">memory_pool</a> of relevant <a href="#classrapidxml_1_1xml__document" kindref="compound">xml_document</a> to allocate the memory. Not only is this faster than allocating them by using <code>new</code> operator, but also their lifetime will be tied to the lifetime of document, possibly simplyfing memory management. <br/><br/>
 Call <a href="#classrapidxml_1_1memory__pool_750ba3c610b129ac057d817509d08f41_1750ba3c610b129ac057d817509d08f41" kindref="member">allocate_node()</a> or <a href="#classrapidxml_1_1memory__pool_462de142669e0ff649e8e615b82bf457_1462de142669e0ff649e8e615b82bf457" kindref="member">allocate_attribute()</a> functions to obtain new nodes or attributes from the pool. You can also call <a href="#classrapidxml_1_1memory__pool_69729185bc59b0875192d667c47b8859_169729185bc59b0875192d667c47b8859" kindref="member">allocate_string()</a> function to allocate strings. Such strings can then be used as names or values of nodes without worrying about their lifetime. Note that there is no <code>free()</code> function -- all allocations are freed at once when <a href="#classrapidxml_1_1memory__pool_c8bb3912a3ce86b15842e79d0b421204_1c8bb3912a3ce86b15842e79d0b421204" kindref="member">clear()</a> function is called, or when the pool is destroyed. <br/><br/>
 It is also possible to create a standalone <a href="#classrapidxml_1_1memory__pool" kindref="compound">memory_pool</a>, and use it to allocate nodes, whose lifetime will not be tied to any document. <br/><br/>
 Pool maintains <code>RAPIDXML_STATIC_POOL_SIZE</code> bytes of statically allocated memory. Until static memory is exhausted, no dynamic memory allocations are done. When static memory is exhausted, pool allocates additional blocks of memory of size <code>RAPIDXML_DYNAMIC_POOL_SIZE</code> each, by using global <code>new[]</code> and <code>delete[]</code> operators. This behaviour can be changed by setting custom allocation routines. Use <a href="#classrapidxml_1_1memory__pool_c0a55a6ef0837dca67572e357100d78a_1c0a55a6ef0837dca67572e357100d78a" kindref="member">set_allocator()</a> function to set them. <br/><br/>
 Allocations for nodes, attributes and strings are aligned at <code>RAPIDXML_ALIGNMENT</code> bytes. This value defaults to the size of pointer on target architecture. <br/><br/>
 To obtain absolutely top performance from the parser, it is important that all nodes are allocated from a single, contiguous block of memory. Otherwise, cache misses when jumping between two (or more) disjoint blocks of memory can slow down parsing quite considerably. If required, you can tweak <code>RAPIDXML_STATIC_POOL_SIZE</code>, <code>RAPIDXML_DYNAMIC_POOL_SIZE</code> and <code>RAPIDXML_ALIGNMENT</code> to obtain best wasted memory to performance compromise. To do it, define their values before <a href="#rapidxml_8hpp" kindref="compound">rapidxml.hpp</a> file is included. </para><h4>Parameters</h4><dl><dt class="parameter-name">Ch</dt><dd>Character type of created nodes. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1memory__pool_f8fb3c8f1a564f8045c40bcd07a89866_1f8fb3c8f1a564f8045c40bcd07a89866">
				constructor
			 memory_pool::memory_pool</h3><h4>Synopsis</h4><code class="synopsis">memory_pool();
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Constructs empty pool with default allocator functions. </para><p/><h3 class="reference-header" id="classrapidxml_1_1memory__pool_6f8c7990d9ec1ed2acf6558b238570eb_16f8c7990d9ec1ed2acf6558b238570eb">
				destructor
			 memory_pool::~memory_pool</h3><h4>Synopsis</h4><code class="synopsis">~memory_pool();
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Destroys pool and frees all the memory. This causes memory occupied by nodes allocated by the pool to be freed. Nodes allocated from the pool are no longer valid. </para><p/><h3 class="reference-header" id="classrapidxml_1_1memory__pool_750ba3c610b129ac057d817509d08f41_1750ba3c610b129ac057d817509d08f41">function memory_pool::allocate_node</h3><h4>Synopsis</h4><code class="synopsis">xml_node&lt;Ch&gt;* allocate_node(node_type type, const Ch *name=0, const Ch *value=0, std::size_t name_size=0, std::size_t value_size=0);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Allocates a new node from the pool, and optionally assigns name and value to it. If the allocation request cannot be accomodated, this function will throw <code>std::bad_alloc</code>. If exceptions are disabled by defining RAPIDXML_NO_EXCEPTIONS, this function will call <a href="#namespacerapidxml_ff5d67f74437199d316d2b2660653ae1_1ff5d67f74437199d316d2b2660653ae1" kindref="member">rapidxml::parse_error_handler()</a> function. </para><h4>Parameters</h4><dl><dt class="parameter-name">type</dt><dd class="parameter-def">Type of node to create. </dd></dl><dl><dt class="parameter-name">name</dt><dd class="parameter-def">Name to assign to the node, or 0 to assign no name. </dd></dl><dl><dt class="parameter-name">value</dt><dd class="parameter-def">Value to assign to the node, or 0 to assign no value. </dd></dl><dl><dt class="parameter-name">name_size</dt><dd class="parameter-def">Size of name to assign, or 0 to automatically calculate size from name string. </dd></dl><dl><dt class="parameter-name">value_size</dt><dd class="parameter-def">Size of value to assign, or 0 to automatically calculate size from value string. </dd></dl><h4>Returns</h4>Pointer to allocated node. This pointer will never be NULL. <p/><h3 class="reference-header" id="classrapidxml_1_1memory__pool_462de142669e0ff649e8e615b82bf457_1462de142669e0ff649e8e615b82bf457">function memory_pool::allocate_attribute</h3><h4>Synopsis</h4><code class="synopsis">xml_attribute&lt;Ch&gt;* allocate_attribute(const Ch *name=0, const Ch *value=0, std::size_t name_size=0, std::size_t value_size=0);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Allocates a new attribute from the pool, and optionally assigns name and value to it. If the allocation request cannot be accomodated, this function will throw <code>std::bad_alloc</code>. If exceptions are disabled by defining RAPIDXML_NO_EXCEPTIONS, this function will call <a href="#namespacerapidxml_ff5d67f74437199d316d2b2660653ae1_1ff5d67f74437199d316d2b2660653ae1" kindref="member">rapidxml::parse_error_handler()</a> function. </para><h4>Parameters</h4><dl><dt class="parameter-name">name</dt><dd class="parameter-def">Name to assign to the attribute, or 0 to assign no name. </dd></dl><dl><dt class="parameter-name">value</dt><dd class="parameter-def">Value to assign to the attribute, or 0 to assign no value. </dd></dl><dl><dt class="parameter-name">name_size</dt><dd class="parameter-def">Size of name to assign, or 0 to automatically calculate size from name string. </dd></dl><dl><dt class="parameter-name">value_size</dt><dd class="parameter-def">Size of value to assign, or 0 to automatically calculate size from value string. </dd></dl><h4>Returns</h4>Pointer to allocated attribute. This pointer will never be NULL. <p/><h3 class="reference-header" id="classrapidxml_1_1memory__pool_69729185bc59b0875192d667c47b8859_169729185bc59b0875192d667c47b8859">function memory_pool::allocate_string</h3><h4>Synopsis</h4><code class="synopsis">Ch* allocate_string(const Ch *source=0, std::size_t size=0);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Allocates a char array of given size from the pool, and optionally copies a given string to it. If the allocation request cannot be accomodated, this function will throw <code>std::bad_alloc</code>. If exceptions are disabled by defining RAPIDXML_NO_EXCEPTIONS, this function will call <a href="#namespacerapidxml_ff5d67f74437199d316d2b2660653ae1_1ff5d67f74437199d316d2b2660653ae1" kindref="member">rapidxml::parse_error_handler()</a> function. </para><h4>Parameters</h4><dl><dt class="parameter-name">source</dt><dd class="parameter-def">String to initialize the allocated memory with, or 0 to not initialize it. </dd></dl><dl><dt class="parameter-name">size</dt><dd class="parameter-def">Number of characters to allocate, or zero to calculate it automatically from source string length; if size is 0, source string must be specified and null terminated. </dd></dl><h4>Returns</h4>Pointer to allocated char array. This pointer will never be NULL. <p/><h3 class="reference-header" id="classrapidxml_1_1memory__pool_95c49fcb056e9103ec906a59e3e01d76_195c49fcb056e9103ec906a59e3e01d76">function memory_pool::clone_node</h3><h4>Synopsis</h4><code class="synopsis">xml_node&lt;Ch&gt;* clone_node(const xml_node&lt; Ch &gt; *source, xml_node&lt; Ch &gt; *result=0);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Clones an <a href="#classrapidxml_1_1xml__node" kindref="compound">xml_node</a> and its hierarchy of child nodes and attributes. Nodes and attributes are allocated from this memory pool. Names and values are not cloned, they are shared between the clone and the source. Result node can be optionally specified as a second parameter, in which case its contents will be replaced with cloned source node. This is useful when you want to clone entire document. </para><h4>Parameters</h4><dl><dt class="parameter-name">source</dt><dd class="parameter-def">Node to clone. </dd></dl><dl><dt class="parameter-name">result</dt><dd class="parameter-def">Node to put results in, or 0 to automatically allocate result node </dd></dl><h4>Returns</h4>Pointer to cloned node. This pointer will never be NULL. <p/><h3 class="reference-header" id="classrapidxml_1_1memory__pool_c8bb3912a3ce86b15842e79d0b421204_1c8bb3912a3ce86b15842e79d0b421204">function memory_pool::clear</h3><h4>Synopsis</h4><code class="synopsis">void clear();
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Clears the pool. This causes memory occupied by nodes allocated by the pool to be freed. Any nodes or strings allocated from the pool will no longer be valid. </para><p/><h3 class="reference-header" id="classrapidxml_1_1memory__pool_c0a55a6ef0837dca67572e357100d78a_1c0a55a6ef0837dca67572e357100d78a">function memory_pool::set_allocator</h3><h4>Synopsis</h4><code class="synopsis">void set_allocator(alloc_func *af, free_func *ff);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Sets or resets the user-defined memory allocation functions for the pool. This can only be called when no memory is allocated from the pool yet, otherwise results are undefined. Allocation function must not return invalid pointer on failure. It should either throw, stop the program, or use <code>longjmp()</code> function to pass control to other place of program. If it returns invalid pointer, results are undefined. <br/><br/>
 User defined allocation functions must have the following forms: <br/><code><br/>
void *allocate(std::size_t size); <br/>
void free(void *pointer); </code><br/></para><h4>Parameters</h4><dl><dt class="parameter-name">af</dt><dd class="parameter-def">Allocation function, or 0 to restore default function </dd></dl><dl><dt class="parameter-name">ff</dt><dd class="parameter-def">Free function, or 0 to restore default function </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1parse__error">class rapidxml::parse_error</h3>

							  Defined in <a href="rapidxml.hpp">rapidxml.hpp</a><br/><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Parse error exception. This exception is thrown by the parser when an error occurs. Use <a href="#classrapidxml_1_1parse__error_ff06f49065b54a8a86e02e9a2441a8ba_1ff06f49065b54a8a86e02e9a2441a8ba" kindref="member">what()</a> function to get human-readable error message. Use <a href="#classrapidxml_1_1parse__error_377be7d201d95221c318682c35377aca_1377be7d201d95221c318682c35377aca" kindref="member">where()</a> function to get a pointer to position within source text where error was detected. <br/><br/>
 If throwing exceptions by the parser is undesirable, it can be disabled by defining RAPIDXML_NO_EXCEPTIONS macro before <a href="#rapidxml_8hpp" kindref="compound">rapidxml.hpp</a> is included. This will cause the parser to call <a href="#namespacerapidxml_ff5d67f74437199d316d2b2660653ae1_1ff5d67f74437199d316d2b2660653ae1" kindref="member">rapidxml::parse_error_handler()</a> function instead of throwing an exception. This function must be defined by the user. <br/><br/>
 This class derives from <code>std::exception</code> class. </para><p/><h3 class="reference-header" id="classrapidxml_1_1parse__error_4dd8d1bdbd9221df4dcb90cafaee3332_14dd8d1bdbd9221df4dcb90cafaee3332">
				constructor
			 parse_error::parse_error</h3><h4>Synopsis</h4><code class="synopsis">parse_error(const char *what, void *where);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Constructs parse error. </para><p/><h3 class="reference-header" id="classrapidxml_1_1parse__error_ff06f49065b54a8a86e02e9a2441a8ba_1ff06f49065b54a8a86e02e9a2441a8ba">function parse_error::what</h3><h4>Synopsis</h4><code class="synopsis">virtual const char* what() const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets human readable description of error. </para><h4>Returns</h4>Pointer to null terminated description of the error. <p/><h3 class="reference-header" id="classrapidxml_1_1parse__error_377be7d201d95221c318682c35377aca_1377be7d201d95221c318682c35377aca">function parse_error::where</h3><h4>Synopsis</h4><code class="synopsis">Ch* where() const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets pointer to character data where error happened. Ch should be the same as char type of <a href="#classrapidxml_1_1xml__document" kindref="compound">xml_document</a> that produced the error. </para><h4>Returns</h4>Pointer to location within the parsed string where error occured. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__attribute">class
									  template
								   rapidxml::xml_attribute</h3>

							  Defined in <a href="rapidxml.hpp">rapidxml.hpp</a><br/>
								  Inherits from
								  <a href="#classrapidxml_1_1xml__base">xml_base</a> <br/><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Class representing attribute node of XML document. Each attribute has name and value strings, which are available through <a href="#classrapidxml_1_1xml__base_622eade29fdf7806d3ef93ac4d90e707_1622eade29fdf7806d3ef93ac4d90e707" kindref="member">name()</a> and <a href="#classrapidxml_1_1xml__base_c54fa4987fb503916a7b541eb15c9c7f_1c54fa4987fb503916a7b541eb15c9c7f" kindref="member">value()</a> functions (inherited from <a href="#classrapidxml_1_1xml__base" kindref="compound">xml_base</a>). Note that after parse, both name and value of attribute will point to interior of source text used for parsing. Thus, this text must persist in memory for the lifetime of attribute. </para><h4>Parameters</h4><dl><dt class="parameter-name">Ch</dt><dd>Character type to use. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__attribute_d5464aadf08269a886b730993525db34_1d5464aadf08269a886b730993525db34">
				constructor
			 xml_attribute::xml_attribute</h3><h4>Synopsis</h4><code class="synopsis">xml_attribute();
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Constructs an empty attribute with the specified type. Consider using <a href="#classrapidxml_1_1memory__pool" kindref="compound">memory_pool</a> of appropriate <a href="#classrapidxml_1_1xml__document" kindref="compound">xml_document</a> if allocating attributes manually. </para><p/><h3 class="reference-header" id="classrapidxml_1_1xml__attribute_77aea7d8d996ba4f6bd61cc478a4e72d_177aea7d8d996ba4f6bd61cc478a4e72d">function xml_attribute::document</h3><h4>Synopsis</h4><code class="synopsis">xml_document&lt;Ch&gt;* document() const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets document of which attribute is a child. </para><h4>Returns</h4>Pointer to document that contains this attribute, or 0 if there is no parent document. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__attribute_5c4a98d2b75f9b41b12c110108fd55ab_15c4a98d2b75f9b41b12c110108fd55ab">function xml_attribute::previous_attribute</h3><h4>Synopsis</h4><code class="synopsis">xml_attribute&lt;Ch&gt;* previous_attribute(const Ch *name=0, std::size_t name_size=0, bool case_sensitive=true) const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets previous attribute, optionally matching attribute name. </para><h4>Parameters</h4><dl><dt class="parameter-name">name</dt><dd class="parameter-def">Name of attribute to find, or 0 to return previous attribute regardless of its name; this string doesn&apos;t have to be zero-terminated if name_size is non-zero </dd></dl><dl><dt class="parameter-name">name_size</dt><dd class="parameter-def">Size of name, in characters, or 0 to have size calculated automatically from string </dd></dl><dl><dt class="parameter-name">case_sensitive</dt><dd class="parameter-def">Should name comparison be case-sensitive; non case-sensitive comparison works properly only for ASCII characters </dd></dl><h4>Returns</h4>Pointer to found attribute, or 0 if not found. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__attribute_1b8a814d0d3a7165396b08433eee8a91_11b8a814d0d3a7165396b08433eee8a91">function xml_attribute::next_attribute</h3><h4>Synopsis</h4><code class="synopsis">xml_attribute&lt;Ch&gt;* next_attribute(const Ch *name=0, std::size_t name_size=0, bool case_sensitive=true) const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets next attribute, optionally matching attribute name. </para><h4>Parameters</h4><dl><dt class="parameter-name">name</dt><dd class="parameter-def">Name of attribute to find, or 0 to return next attribute regardless of its name; this string doesn&apos;t have to be zero-terminated if name_size is non-zero </dd></dl><dl><dt class="parameter-name">name_size</dt><dd class="parameter-def">Size of name, in characters, or 0 to have size calculated automatically from string </dd></dl><dl><dt class="parameter-name">case_sensitive</dt><dd class="parameter-def">Should name comparison be case-sensitive; non case-sensitive comparison works properly only for ASCII characters </dd></dl><h4>Returns</h4>Pointer to found attribute, or 0 if not found. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__base">class
									  template
								   rapidxml::xml_base</h3>

							  Defined in <a href="rapidxml.hpp">rapidxml.hpp</a><br/>
								  Base class for
								  <a href="#classrapidxml_1_1xml__attribute">xml_attribute</a> <a href="#classrapidxml_1_1xml__node">xml_node</a> <h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Base class for <a href="#classrapidxml_1_1xml__node" kindref="compound">xml_node</a> and <a href="#classrapidxml_1_1xml__attribute" kindref="compound">xml_attribute</a> implementing common functions: <a href="#classrapidxml_1_1xml__base_622eade29fdf7806d3ef93ac4d90e707_1622eade29fdf7806d3ef93ac4d90e707" kindref="member">name()</a>, <a href="#classrapidxml_1_1xml__base_0dae694c8f7e4d89f1003e2f3a15a43c_10dae694c8f7e4d89f1003e2f3a15a43c" kindref="member">name_size()</a>, <a href="#classrapidxml_1_1xml__base_c54fa4987fb503916a7b541eb15c9c7f_1c54fa4987fb503916a7b541eb15c9c7f" kindref="member">value()</a>, <a href="#classrapidxml_1_1xml__base_aed5ae791b7164c1ee5e649198cbb3db_1aed5ae791b7164c1ee5e649198cbb3db" kindref="member">value_size()</a> and <a href="#classrapidxml_1_1xml__base_798e8df7ea53ade4d9f0701017dce80e_1798e8df7ea53ade4d9f0701017dce80e" kindref="member">parent()</a>. </para><h4>Parameters</h4><dl><dt class="parameter-name">Ch</dt><dd>Character type to use </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__base_23630d2c130a9e0e3f3afa7584a9b218_123630d2c130a9e0e3f3afa7584a9b218">
				constructor
			 xml_base::xml_base</h3><h4>Synopsis</h4><code class="synopsis">xml_base();
									  </code><p/><h3 class="reference-header" id="classrapidxml_1_1xml__base_622eade29fdf7806d3ef93ac4d90e707_1622eade29fdf7806d3ef93ac4d90e707">function xml_base::name</h3><h4>Synopsis</h4><code class="synopsis">Ch* name() const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets name of the node. Interpretation of name depends on type of node. Note that name will not be zero-terminated if <a href="#namespacerapidxml_9cae3801e70437cbc410c24bf6be691c_19cae3801e70437cbc410c24bf6be691c" kindref="member">rapidxml::parse_no_string_terminators</a> option was selected during parse. <br/><br/>
 Use <a href="#classrapidxml_1_1xml__base_0dae694c8f7e4d89f1003e2f3a15a43c_10dae694c8f7e4d89f1003e2f3a15a43c" kindref="member">name_size()</a> function to determine length of the name. </para><h4>Returns</h4>Name of node, or empty string if node has no name. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__base_0dae694c8f7e4d89f1003e2f3a15a43c_10dae694c8f7e4d89f1003e2f3a15a43c">function xml_base::name_size</h3><h4>Synopsis</h4><code class="synopsis">std::size_t name_size() const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets size of node name, not including terminator character. This function works correctly irrespective of whether name is or is not zero terminated. </para><h4>Returns</h4>Size of node name, in characters. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__base_c54fa4987fb503916a7b541eb15c9c7f_1c54fa4987fb503916a7b541eb15c9c7f">function xml_base::value</h3><h4>Synopsis</h4><code class="synopsis">Ch* value() const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets value of node. Interpretation of value depends on type of node. Note that value will not be zero-terminated if <a href="#namespacerapidxml_9cae3801e70437cbc410c24bf6be691c_19cae3801e70437cbc410c24bf6be691c" kindref="member">rapidxml::parse_no_string_terminators</a> option was selected during parse. <br/><br/>
 Use <a href="#classrapidxml_1_1xml__base_aed5ae791b7164c1ee5e649198cbb3db_1aed5ae791b7164c1ee5e649198cbb3db" kindref="member">value_size()</a> function to determine length of the value. </para><h4>Returns</h4>Value of node, or empty string if node has no value. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__base_aed5ae791b7164c1ee5e649198cbb3db_1aed5ae791b7164c1ee5e649198cbb3db">function xml_base::value_size</h3><h4>Synopsis</h4><code class="synopsis">std::size_t value_size() const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets size of node value, not including terminator character. This function works correctly irrespective of whether value is or is not zero terminated. </para><h4>Returns</h4>Size of node value, in characters. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__base_4e7e23d06d48126c65b1f6266acfba5c_14e7e23d06d48126c65b1f6266acfba5c">function xml_base::name</h3><h4>Synopsis</h4><code class="synopsis">void name(const Ch *name, std::size_t size);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Sets name of node to a non zero-terminated string. See <a href="#namespacerapidxml_1ownership_of_strings" kindref="member">Ownership Of Strings</a> . <br/><br/>
 Note that node does not own its name or value, it only stores a pointer to it. It will not delete or otherwise free the pointer on destruction. It is reponsibility of the user to properly manage lifetime of the string. The easiest way to achieve it is to use <a href="#classrapidxml_1_1memory__pool" kindref="compound">memory_pool</a> of the document to allocate the string - on destruction of the document the string will be automatically freed. <br/><br/>
 Size of name must be specified separately, because name does not have to be zero terminated. Use <a href="#classrapidxml_1_1xml__base_e099c291e104a0d277307fe71f5e0f9e_1e099c291e104a0d277307fe71f5e0f9e" kindref="member">name(const Ch *)</a> function to have the length automatically calculated (string must be zero terminated). </para><h4>Parameters</h4><dl><dt class="parameter-name">name</dt><dd class="parameter-def">Name of node to set. Does not have to be zero terminated. </dd></dl><dl><dt class="parameter-name">size</dt><dd class="parameter-def">Size of name, in characters. This does not include zero terminator, if one is present. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__base_e099c291e104a0d277307fe71f5e0f9e_1e099c291e104a0d277307fe71f5e0f9e">function xml_base::name</h3><h4>Synopsis</h4><code class="synopsis">void name(const Ch *name);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Sets name of node to a zero-terminated string. See also <a href="#namespacerapidxml_1ownership_of_strings" kindref="member">Ownership Of Strings</a>  and <a href="#classrapidxml_1_1xml__base_4e7e23d06d48126c65b1f6266acfba5c_14e7e23d06d48126c65b1f6266acfba5c" kindref="member">xml_node::name(const Ch *, std::size_t)</a>. </para><h4>Parameters</h4><dl><dt class="parameter-name">name</dt><dd class="parameter-def">Name of node to set. Must be zero terminated. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__base_d9640aa3f5374673cb72a5289b6c91eb_1d9640aa3f5374673cb72a5289b6c91eb">function xml_base::value</h3><h4>Synopsis</h4><code class="synopsis">void value(const Ch *value, std::size_t size);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Sets value of node to a non zero-terminated string. See <a href="#namespacerapidxml_1ownership_of_strings" kindref="member">Ownership Of Strings</a> . <br/><br/>
 Note that node does not own its name or value, it only stores a pointer to it. It will not delete or otherwise free the pointer on destruction. It is reponsibility of the user to properly manage lifetime of the string. The easiest way to achieve it is to use <a href="#classrapidxml_1_1memory__pool" kindref="compound">memory_pool</a> of the document to allocate the string - on destruction of the document the string will be automatically freed. <br/><br/>
 Size of value must be specified separately, because it does not have to be zero terminated. Use <a href="#classrapidxml_1_1xml__base_18c7469acdca771de9b4f3054053029c_118c7469acdca771de9b4f3054053029c" kindref="member">value(const Ch *)</a> function to have the length automatically calculated (string must be zero terminated). <br/><br/>
 If an element has a child node of type node_data, it will take precedence over element value when printing. If you want to manipulate data of elements using values, use parser flag <a href="#namespacerapidxml_87e8bbab53702cf3b438bd553c10b6b9_187e8bbab53702cf3b438bd553c10b6b9" kindref="member">rapidxml::parse_no_data_nodes</a> to prevent creation of data nodes by the parser. </para><h4>Parameters</h4><dl><dt class="parameter-name">value</dt><dd class="parameter-def">value of node to set. Does not have to be zero terminated. </dd></dl><dl><dt class="parameter-name">size</dt><dd class="parameter-def">Size of value, in characters. This does not include zero terminator, if one is present. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__base_18c7469acdca771de9b4f3054053029c_118c7469acdca771de9b4f3054053029c">function xml_base::value</h3><h4>Synopsis</h4><code class="synopsis">void value(const Ch *value);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Sets value of node to a zero-terminated string. See also <a href="#namespacerapidxml_1ownership_of_strings" kindref="member">Ownership Of Strings</a>  and <a href="#classrapidxml_1_1xml__base_d9640aa3f5374673cb72a5289b6c91eb_1d9640aa3f5374673cb72a5289b6c91eb" kindref="member">xml_node::value(const Ch *, std::size_t)</a>. </para><h4>Parameters</h4><dl><dt class="parameter-name">value</dt><dd class="parameter-def">Vame of node to set. Must be zero terminated. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__base_798e8df7ea53ade4d9f0701017dce80e_1798e8df7ea53ade4d9f0701017dce80e">function xml_base::parent</h3><h4>Synopsis</h4><code class="synopsis">xml_node&lt;Ch&gt;* parent() const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets node parent. </para><h4>Returns</h4>Pointer to parent node, or 0 if there is no parent. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__document">class
									  template
								   rapidxml::xml_document</h3>

							  Defined in <a href="rapidxml.hpp">rapidxml.hpp</a><br/>
								  Inherits from
								  <a href="#classrapidxml_1_1xml__node">xml_node</a> <a href="#classrapidxml_1_1memory__pool">memory_pool</a> <br/><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">This class represents root of the DOM hierarchy. It is also an <a href="#classrapidxml_1_1xml__node" kindref="compound">xml_node</a> and a <a href="#classrapidxml_1_1memory__pool" kindref="compound">memory_pool</a> through public inheritance. Use <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">parse()</a> function to build a DOM tree from a zero-terminated XML text string. <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">parse()</a> function allocates memory for nodes and attributes by using functions of <a href="#classrapidxml_1_1xml__document" kindref="compound">xml_document</a>, which are inherited from <a href="#classrapidxml_1_1memory__pool" kindref="compound">memory_pool</a>. To access root node of the document, use the document itself, as if it was an <a href="#classrapidxml_1_1xml__node" kindref="compound">xml_node</a>. </para><h4>Parameters</h4><dl><dt class="parameter-name">Ch</dt><dd>Character type to use. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__document_6ce266cc52d549c42abe3a3d5e8af9ba_16ce266cc52d549c42abe3a3d5e8af9ba">
				constructor
			 xml_document::xml_document</h3><h4>Synopsis</h4><code class="synopsis">xml_document();
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Constructs empty XML document. </para><p/><h3 class="reference-header" id="classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c">function xml_document::parse</h3><h4>Synopsis</h4><code class="synopsis">void parse(Ch *text);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Parses zero-terminated XML string according to given flags. Passed string will be modified by the parser, unless <a href="#namespacerapidxml_a97ba1a0a79a6d66f4eef3612508d943_1a97ba1a0a79a6d66f4eef3612508d943" kindref="member">rapidxml::parse_non_destructive</a> flag is used. The string must persist for the lifetime of the document. In case of error, <a href="#classrapidxml_1_1parse__error" kindref="compound">rapidxml::parse_error</a> exception will be thrown. <br/><br/>
 If you want to parse contents of a file, you must first load the file into the memory, and pass pointer to its beginning. Make sure that data is zero-terminated. <br/><br/>
 Document can be parsed into multiple times. Each new call to parse removes previous nodes and attributes (if any), but does not clear memory pool. </para><h4>Parameters</h4><dl><dt class="parameter-name">text</dt><dd class="parameter-def">XML data to parse; pointer is non-const to denote fact that this data may be modified by the parser. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__document_c8bb3912a3ce86b15842e79d0b421204_1c8bb3912a3ce86b15842e79d0b421204">function xml_document::clear</h3><h4>Synopsis</h4><code class="synopsis">void clear();
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Clears the document by deleting all nodes and clearing the memory pool. All nodes owned by document pool are destroyed. </para><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node">class
									  template
								   rapidxml::xml_node</h3>

							  Defined in <a href="rapidxml.hpp">rapidxml.hpp</a><br/>
								  Inherits from
								  <a href="#classrapidxml_1_1xml__base">xml_base</a> <br/>
								  Base class for
								  <a href="#classrapidxml_1_1xml__document">xml_document</a> <h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Class representing a node of XML document. Each node may have associated name and value strings, which are available through <a href="#classrapidxml_1_1xml__base_622eade29fdf7806d3ef93ac4d90e707_1622eade29fdf7806d3ef93ac4d90e707" kindref="member">name()</a> and <a href="#classrapidxml_1_1xml__base_c54fa4987fb503916a7b541eb15c9c7f_1c54fa4987fb503916a7b541eb15c9c7f" kindref="member">value()</a> functions. Interpretation of name and value depends on type of the node. Type of node can be determined by using <a href="#classrapidxml_1_1xml__node_975e86937621ae4afe6a423219de30d0_1975e86937621ae4afe6a423219de30d0" kindref="member">type()</a> function. <br/><br/>
 Note that after parse, both name and value of node, if any, will point interior of source text used for parsing. Thus, this text must persist in the memory for the lifetime of node. </para><h4>Parameters</h4><dl><dt class="parameter-name">Ch</dt><dd>Character type to use. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_34c55af3504549a475e5b9dfcaa6adf5_134c55af3504549a475e5b9dfcaa6adf5">
				constructor
			 xml_node::xml_node</h3><h4>Synopsis</h4><code class="synopsis">xml_node(node_type type);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Constructs an empty node with the specified type. Consider using <a href="#classrapidxml_1_1memory__pool" kindref="compound">memory_pool</a> of appropriate document to allocate nodes manually. </para><h4>Parameters</h4><dl><dt class="parameter-name">type</dt><dd class="parameter-def">Type of node to construct. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_975e86937621ae4afe6a423219de30d0_1975e86937621ae4afe6a423219de30d0">function xml_node::type</h3><h4>Synopsis</h4><code class="synopsis">node_type type() const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets type of node. </para><h4>Returns</h4>Type of node. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_77aea7d8d996ba4f6bd61cc478a4e72d_177aea7d8d996ba4f6bd61cc478a4e72d">function xml_node::document</h3><h4>Synopsis</h4><code class="synopsis">xml_document&lt;Ch&gt;* document() const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets document of which node is a child. </para><h4>Returns</h4>Pointer to document that contains this node, or 0 if there is no parent document. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_7823e36687669e59c2afdf66334ef35a_17823e36687669e59c2afdf66334ef35a">function xml_node::first_node</h3><h4>Synopsis</h4><code class="synopsis">xml_node&lt;Ch&gt;* first_node(const Ch *name=0, std::size_t name_size=0, bool case_sensitive=true) const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets first child node, optionally matching node name. </para><h4>Parameters</h4><dl><dt class="parameter-name">name</dt><dd class="parameter-def">Name of child to find, or 0 to return first child regardless of its name; this string doesn&apos;t have to be zero-terminated if name_size is non-zero </dd></dl><dl><dt class="parameter-name">name_size</dt><dd class="parameter-def">Size of name, in characters, or 0 to have size calculated automatically from string </dd></dl><dl><dt class="parameter-name">case_sensitive</dt><dd class="parameter-def">Should name comparison be case-sensitive; non case-sensitive comparison works properly only for ASCII characters </dd></dl><h4>Returns</h4>Pointer to found child, or 0 if not found. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_fcb6e2209b591a36d2dadba20d2bc7cc_1fcb6e2209b591a36d2dadba20d2bc7cc">function xml_node::last_node</h3><h4>Synopsis</h4><code class="synopsis">xml_node&lt;Ch&gt;* last_node(const Ch *name=0, std::size_t name_size=0, bool case_sensitive=true) const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets last child node, optionally matching node name. Behaviour is undefined if node has no children. Use <a href="#classrapidxml_1_1xml__node_7823e36687669e59c2afdf66334ef35a_17823e36687669e59c2afdf66334ef35a" kindref="member">first_node()</a> to test if node has children. </para><h4>Parameters</h4><dl><dt class="parameter-name">name</dt><dd class="parameter-def">Name of child to find, or 0 to return last child regardless of its name; this string doesn&apos;t have to be zero-terminated if name_size is non-zero </dd></dl><dl><dt class="parameter-name">name_size</dt><dd class="parameter-def">Size of name, in characters, or 0 to have size calculated automatically from string </dd></dl><dl><dt class="parameter-name">case_sensitive</dt><dd class="parameter-def">Should name comparison be case-sensitive; non case-sensitive comparison works properly only for ASCII characters </dd></dl><h4>Returns</h4>Pointer to found child, or 0 if not found. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_ac2f6886c0107e9d5f156e9542546df6_1ac2f6886c0107e9d5f156e9542546df6">function xml_node::previous_sibling</h3><h4>Synopsis</h4><code class="synopsis">xml_node&lt;Ch&gt;* previous_sibling(const Ch *name=0, std::size_t name_size=0, bool case_sensitive=true) const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets previous sibling node, optionally matching node name. Behaviour is undefined if node has no parent. Use <a href="#classrapidxml_1_1xml__base_798e8df7ea53ade4d9f0701017dce80e_1798e8df7ea53ade4d9f0701017dce80e" kindref="member">parent()</a> to test if node has a parent. </para><h4>Parameters</h4><dl><dt class="parameter-name">name</dt><dd class="parameter-def">Name of sibling to find, or 0 to return previous sibling regardless of its name; this string doesn&apos;t have to be zero-terminated if name_size is non-zero </dd></dl><dl><dt class="parameter-name">name_size</dt><dd class="parameter-def">Size of name, in characters, or 0 to have size calculated automatically from string </dd></dl><dl><dt class="parameter-name">case_sensitive</dt><dd class="parameter-def">Should name comparison be case-sensitive; non case-sensitive comparison works properly only for ASCII characters </dd></dl><h4>Returns</h4>Pointer to found sibling, or 0 if not found. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_b3ead2cefecc03a813836203e3f6f38f_1b3ead2cefecc03a813836203e3f6f38f">function xml_node::next_sibling</h3><h4>Synopsis</h4><code class="synopsis">xml_node&lt;Ch&gt;* next_sibling(const Ch *name=0, std::size_t name_size=0, bool case_sensitive=true) const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets next sibling node, optionally matching node name. Behaviour is undefined if node has no parent. Use <a href="#classrapidxml_1_1xml__base_798e8df7ea53ade4d9f0701017dce80e_1798e8df7ea53ade4d9f0701017dce80e" kindref="member">parent()</a> to test if node has a parent. </para><h4>Parameters</h4><dl><dt class="parameter-name">name</dt><dd class="parameter-def">Name of sibling to find, or 0 to return next sibling regardless of its name; this string doesn&apos;t have to be zero-terminated if name_size is non-zero </dd></dl><dl><dt class="parameter-name">name_size</dt><dd class="parameter-def">Size of name, in characters, or 0 to have size calculated automatically from string </dd></dl><dl><dt class="parameter-name">case_sensitive</dt><dd class="parameter-def">Should name comparison be case-sensitive; non case-sensitive comparison works properly only for ASCII characters </dd></dl><h4>Returns</h4>Pointer to found sibling, or 0 if not found. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_5810a09f82f8d53efbe9456286dcec83_15810a09f82f8d53efbe9456286dcec83">function xml_node::first_attribute</h3><h4>Synopsis</h4><code class="synopsis">xml_attribute&lt;Ch&gt;* first_attribute(const Ch *name=0, std::size_t name_size=0, bool case_sensitive=true) const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets first attribute of node, optionally matching attribute name. </para><h4>Parameters</h4><dl><dt class="parameter-name">name</dt><dd class="parameter-def">Name of attribute to find, or 0 to return first attribute regardless of its name; this string doesn&apos;t have to be zero-terminated if name_size is non-zero </dd></dl><dl><dt class="parameter-name">name_size</dt><dd class="parameter-def">Size of name, in characters, or 0 to have size calculated automatically from string </dd></dl><dl><dt class="parameter-name">case_sensitive</dt><dd class="parameter-def">Should name comparison be case-sensitive; non case-sensitive comparison works properly only for ASCII characters </dd></dl><h4>Returns</h4>Pointer to found attribute, or 0 if not found. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_16953d66751b5b949ee4ee2d9c0bc63a_116953d66751b5b949ee4ee2d9c0bc63a">function xml_node::last_attribute</h3><h4>Synopsis</h4><code class="synopsis">xml_attribute&lt;Ch&gt;* last_attribute(const Ch *name=0, std::size_t name_size=0, bool case_sensitive=true) const;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Gets last attribute of node, optionally matching attribute name. </para><h4>Parameters</h4><dl><dt class="parameter-name">name</dt><dd class="parameter-def">Name of attribute to find, or 0 to return last attribute regardless of its name; this string doesn&apos;t have to be zero-terminated if name_size is non-zero </dd></dl><dl><dt class="parameter-name">name_size</dt><dd class="parameter-def">Size of name, in characters, or 0 to have size calculated automatically from string </dd></dl><dl><dt class="parameter-name">case_sensitive</dt><dd class="parameter-def">Should name comparison be case-sensitive; non case-sensitive comparison works properly only for ASCII characters </dd></dl><h4>Returns</h4>Pointer to found attribute, or 0 if not found. <p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_a78759bfa429fa2ab6bc5fe617cfa3cf_1a78759bfa429fa2ab6bc5fe617cfa3cf">function xml_node::type</h3><h4>Synopsis</h4><code class="synopsis">void type(node_type type);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Sets type of node. </para><h4>Parameters</h4><dl><dt class="parameter-name">type</dt><dd class="parameter-def">Type of node to set. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_0c39df6617e709eb2fba11300dea63f2_10c39df6617e709eb2fba11300dea63f2">function xml_node::prepend_node</h3><h4>Synopsis</h4><code class="synopsis">void prepend_node(xml_node&lt; Ch &gt; *child);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Prepends a new child node. The prepended child becomes the first child, and all existing children are moved one position back. </para><h4>Parameters</h4><dl><dt class="parameter-name">child</dt><dd class="parameter-def">Node to prepend. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_86de2e22276826089b7baed2599f8dee_186de2e22276826089b7baed2599f8dee">function xml_node::append_node</h3><h4>Synopsis</h4><code class="synopsis">void append_node(xml_node&lt; Ch &gt; *child);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Appends a new child node. The appended child becomes the last child. </para><h4>Parameters</h4><dl><dt class="parameter-name">child</dt><dd class="parameter-def">Node to append. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_780972a57fc447250ab47cc8f421b65e_1780972a57fc447250ab47cc8f421b65e">function xml_node::insert_node</h3><h4>Synopsis</h4><code class="synopsis">void insert_node(xml_node&lt; Ch &gt; *where, xml_node&lt; Ch &gt; *child);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Inserts a new child node at specified place inside the node. All children after and including the specified node are moved one position back. </para><h4>Parameters</h4><dl><dt class="parameter-name">where</dt><dd class="parameter-def">Place where to insert the child, or 0 to insert at the back. </dd></dl><dl><dt class="parameter-name">child</dt><dd class="parameter-def">Node to insert. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_9a31d861e1bddc710839c551a5d2b3a4_19a31d861e1bddc710839c551a5d2b3a4">function xml_node::remove_first_node</h3><h4>Synopsis</h4><code class="synopsis">void remove_first_node();
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Removes first child node. If node has no children, behaviour is undefined. Use <a href="#classrapidxml_1_1xml__node_7823e36687669e59c2afdf66334ef35a_17823e36687669e59c2afdf66334ef35a" kindref="member">first_node()</a> to test if node has children. </para><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_87addf2bc127ee31aa4b5295d3c9b530_187addf2bc127ee31aa4b5295d3c9b530">function xml_node::remove_last_node</h3><h4>Synopsis</h4><code class="synopsis">void remove_last_node();
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Removes last child of the node. If node has no children, behaviour is undefined. Use <a href="#classrapidxml_1_1xml__node_7823e36687669e59c2afdf66334ef35a_17823e36687669e59c2afdf66334ef35a" kindref="member">first_node()</a> to test if node has children. </para><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_9316463a2201631e7e2062b17729f9cd_19316463a2201631e7e2062b17729f9cd">function xml_node::remove_node</h3><h4>Synopsis</h4><code class="synopsis">void remove_node(xml_node&lt; Ch &gt; *where);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Removes specified child from the node. </para><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_0218147d13e41d5fa60ced4e7a7e9726_10218147d13e41d5fa60ced4e7a7e9726">function xml_node::remove_all_nodes</h3><h4>Synopsis</h4><code class="synopsis">void remove_all_nodes();
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Removes all child nodes (but not attributes). </para><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_f6dffa513da74cc0be71a7ba84f8265e_1f6dffa513da74cc0be71a7ba84f8265e">function xml_node::prepend_attribute</h3><h4>Synopsis</h4><code class="synopsis">void prepend_attribute(xml_attribute&lt; Ch &gt; *attribute);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Prepends a new attribute to the node. </para><h4>Parameters</h4><dl><dt class="parameter-name">attribute</dt><dd class="parameter-def">Attribute to prepend. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_8fbd4f5ef7169d493da9f8d87ac04b77_18fbd4f5ef7169d493da9f8d87ac04b77">function xml_node::append_attribute</h3><h4>Synopsis</h4><code class="synopsis">void append_attribute(xml_attribute&lt; Ch &gt; *attribute);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Appends a new attribute to the node. </para><h4>Parameters</h4><dl><dt class="parameter-name">attribute</dt><dd class="parameter-def">Attribute to append. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_070d5888b0557fe06a5b24961de1b988_1070d5888b0557fe06a5b24961de1b988">function xml_node::insert_attribute</h3><h4>Synopsis</h4><code class="synopsis">void insert_attribute(xml_attribute&lt; Ch &gt; *where, xml_attribute&lt; Ch &gt; *attribute);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Inserts a new attribute at specified place inside the node. All attributes after and including the specified attribute are moved one position back. </para><h4>Parameters</h4><dl><dt class="parameter-name">where</dt><dd class="parameter-def">Place where to insert the attribute, or 0 to insert at the back. </dd></dl><dl><dt class="parameter-name">attribute</dt><dd class="parameter-def">Attribute to insert. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_4eea4a7f6cb484ca9944f7eafe6e1843_14eea4a7f6cb484ca9944f7eafe6e1843">function xml_node::remove_first_attribute</h3><h4>Synopsis</h4><code class="synopsis">void remove_first_attribute();
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Removes first attribute of the node. If node has no attributes, behaviour is undefined. Use <a href="#classrapidxml_1_1xml__node_5810a09f82f8d53efbe9456286dcec83_15810a09f82f8d53efbe9456286dcec83" kindref="member">first_attribute()</a> to test if node has attributes. </para><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_37d87c4d5d89fa0cf05b72ee8d4cba3b_137d87c4d5d89fa0cf05b72ee8d4cba3b">function xml_node::remove_last_attribute</h3><h4>Synopsis</h4><code class="synopsis">void remove_last_attribute();
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Removes last attribute of the node. If node has no attributes, behaviour is undefined. Use <a href="#classrapidxml_1_1xml__node_5810a09f82f8d53efbe9456286dcec83_15810a09f82f8d53efbe9456286dcec83" kindref="member">first_attribute()</a> to test if node has attributes. </para><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_c75154db2e768c0e5b541fc8cd0775ab_1c75154db2e768c0e5b541fc8cd0775ab">function xml_node::remove_attribute</h3><h4>Synopsis</h4><code class="synopsis">void remove_attribute(xml_attribute&lt; Ch &gt; *where);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Removes specified attribute from node. </para><h4>Parameters</h4><dl><dt class="parameter-name">where</dt><dd class="parameter-def">Pointer to attribute to be removed. </dd></dl><p/><h3 class="reference-header" id="classrapidxml_1_1xml__node_59e6ad4cfd5e8096c052e71d79561eda_159e6ad4cfd5e8096c052e71d79561eda">function xml_node::remove_all_attributes</h3><h4>Synopsis</h4><code class="synopsis">void remove_all_attributes();
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Removes all attributes of node. </para><p/><h3 class="reference-header" id="namespacerapidxml_6a276b85e2da28c5f9c3dbce61c55682_16a276b85e2da28c5f9c3dbce61c55682">enum node_type</h3><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Enumeration listing all node types produced by the parser. Use <a href="#classrapidxml_1_1xml__node_975e86937621ae4afe6a423219de30d0_1975e86937621ae4afe6a423219de30d0" kindref="member">xml_node::type()</a> function to query node type. </para><h4>Values</h4><dl><dt class="parameter-name">node_document</dt><dd class="parameter-def">A document node. Name and value are empty. </dd></dl><dl><dt class="parameter-name">node_element</dt><dd class="parameter-def">An element node. Name contains element name. Value contains text of first data node. </dd></dl><dl><dt class="parameter-name">node_data</dt><dd class="parameter-def">A data node. Name is empty. Value contains data text. </dd></dl><dl><dt class="parameter-name">node_cdata</dt><dd class="parameter-def">A CDATA node. Name is empty. Value contains data text. </dd></dl><dl><dt class="parameter-name">node_comment</dt><dd class="parameter-def">A comment node. Name is empty. Value contains comment text. </dd></dl><dl><dt class="parameter-name">node_declaration</dt><dd class="parameter-def">A declaration node. Name and value are empty. Declaration parameters (version, encoding and standalone) are in node attributes. </dd></dl><dl><dt class="parameter-name">node_doctype</dt><dd class="parameter-def">A DOCTYPE node. Name is empty. Value contains DOCTYPE text. </dd></dl><dl><dt class="parameter-name">node_pi</dt><dd class="parameter-def">A PI node. Name contains target. Value contains instructions. </dd></dl><p/><h3 class="reference-header" id="namespacerapidxml_ff5d67f74437199d316d2b2660653ae1_1ff5d67f74437199d316d2b2660653ae1">function parse_error_handler</h3><h4>Synopsis</h4><code class="synopsis">void rapidxml::parse_error_handler(const char *what, void *where);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">When exceptions are disabled by defining RAPIDXML_NO_EXCEPTIONS, this function is called to notify user about the error. It must be defined by the user. <br/><br/>
 This function cannot return. If it does, the results are undefined. <br/><br/>
 A very simple definition might look like that: <preformatted>
        void rapidxml::parse_error_handler(const char *what, void *where)
        {
            std::cout &lt;&lt; &quot;Parse error: &quot; &lt;&lt; what &lt;&lt; &quot;\n&quot;;
            std::abort();
        }
        </preformatted></para><h4>Parameters</h4><dl><dt class="parameter-name">what</dt><dd class="parameter-def">Human readable description of the error. </dd></dl><dl><dt class="parameter-name">where</dt><dd class="parameter-def">Pointer to character data where error was detected. </dd></dl><p/><h3 class="reference-header" id="namespacerapidxml_b94d570fc4c4ab2423813cd0243326b1_1b94d570fc4c4ab2423813cd0243326b1">function print</h3><h4>Synopsis</h4><code class="synopsis">OutIt rapidxml::print(OutIt out, const xml_node&lt; Ch &gt; &amp;node, int flags=0);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Prints XML to given output iterator. </para><h4>Parameters</h4><dl><dt class="parameter-name">out</dt><dd class="parameter-def">Output iterator to print to. </dd></dl><dl><dt class="parameter-name">node</dt><dd class="parameter-def">Node to be printed. Pass xml_document to print entire document. </dd></dl><dl><dt class="parameter-name">flags</dt><dd class="parameter-def">Flags controlling how XML is printed. </dd></dl><h4>Returns</h4>Output iterator pointing to position immediately after last character of printed text. <p/><h3 class="reference-header" id="namespacerapidxml_13bc37d6d1047acb0efdbc1689221a5e_113bc37d6d1047acb0efdbc1689221a5e">function print</h3><h4>Synopsis</h4><code class="synopsis">std::basic_ostream&lt;Ch&gt;&amp; rapidxml::print(std::basic_ostream&lt; Ch &gt; &amp;out, const xml_node&lt; Ch &gt; &amp;node, int flags=0);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Prints XML to given output stream. </para><h4>Parameters</h4><dl><dt class="parameter-name">out</dt><dd class="parameter-def">Output stream to print to. </dd></dl><dl><dt class="parameter-name">node</dt><dd class="parameter-def">Node to be printed. Pass xml_document to print entire document. </dd></dl><dl><dt class="parameter-name">flags</dt><dd class="parameter-def">Flags controlling how XML is printed. </dd></dl><h4>Returns</h4>Output stream. <p/><h3 class="reference-header" id="namespacerapidxml_5619b38000d967fb223b2b0a8c17463a_15619b38000d967fb223b2b0a8c17463a">function operator&lt;&lt;</h3><h4>Synopsis</h4><code class="synopsis">std::basic_ostream&lt;Ch&gt;&amp; rapidxml::operator&lt;&lt;(std::basic_ostream&lt; Ch &gt; &amp;out, const xml_node&lt; Ch &gt; &amp;node);
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Prints formatted XML to given output stream. Uses default printing flags. Use <a href="#namespacerapidxml_b94d570fc4c4ab2423813cd0243326b1_1b94d570fc4c4ab2423813cd0243326b1" kindref="member">print()</a> function to customize printing process. </para><h4>Parameters</h4><dl><dt class="parameter-name">out</dt><dd class="parameter-def">Output stream to print to. </dd></dl><dl><dt class="parameter-name">node</dt><dd class="parameter-def">Node to be printed. </dd></dl><h4>Returns</h4>Output stream. <p/><h3 class="reference-header" id="namespacerapidxml_87e8bbab53702cf3b438bd553c10b6b9_187e8bbab53702cf3b438bd553c10b6b9">
				constant
			 parse_no_data_nodes</h3><h4>Synopsis</h4><code class="synopsis">const int parse_no_data_nodes
											  = 0x1;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Parse flag instructing the parser to not create data nodes. Text of first data node will still be placed in value of parent element, unless <a href="#namespacerapidxml_97e2c4fdc04fae17126f9971a4fc993e_197e2c4fdc04fae17126f9971a4fc993e" kindref="member">rapidxml::parse_no_element_values</a> flag is also specified. Can be combined with other flags by use of | operator. <br/><br/>
 See <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function. </para><p/><h3 class="reference-header" id="namespacerapidxml_97e2c4fdc04fae17126f9971a4fc993e_197e2c4fdc04fae17126f9971a4fc993e">
				constant
			 parse_no_element_values</h3><h4>Synopsis</h4><code class="synopsis">const int parse_no_element_values
											  = 0x2;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Parse flag instructing the parser to not use text of first data node as a value of parent element. Can be combined with other flags by use of | operator. Note that child data nodes of element node take precendence over its value when printing. That is, if element has one or more child data nodes <i>and</i> a value, the value will be ignored. Use <a href="#namespacerapidxml_87e8bbab53702cf3b438bd553c10b6b9_187e8bbab53702cf3b438bd553c10b6b9" kindref="member">rapidxml::parse_no_data_nodes</a> flag to prevent creation of data nodes if you want to manipulate data using values of elements. <br/><br/>
 See <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function. </para><p/><h3 class="reference-header" id="namespacerapidxml_9cae3801e70437cbc410c24bf6be691c_19cae3801e70437cbc410c24bf6be691c">
				constant
			 parse_no_string_terminators</h3><h4>Synopsis</h4><code class="synopsis">const int parse_no_string_terminators
											  = 0x4;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Parse flag instructing the parser to not place zero terminators after strings in the source text. By default zero terminators are placed, modifying source text. Can be combined with other flags by use of | operator. <br/><br/>
 See <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function. </para><p/><h3 class="reference-header" id="namespacerapidxml_7223b7815c4fb8b42e6e4e77e1ea6b97_17223b7815c4fb8b42e6e4e77e1ea6b97">
				constant
			 parse_no_entity_translation</h3><h4>Synopsis</h4><code class="synopsis">const int parse_no_entity_translation
											  = 0x8;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Parse flag instructing the parser to not translate entities in the source text. By default entities are translated, modifying source text. Can be combined with other flags by use of | operator. <br/><br/>
 See <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function. </para><p/><h3 class="reference-header" id="namespacerapidxml_ccde57f6054857ee4042a1b4d98c83b9_1ccde57f6054857ee4042a1b4d98c83b9">
				constant
			 parse_no_utf8</h3><h4>Synopsis</h4><code class="synopsis">const int parse_no_utf8
											  = 0x10;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Parse flag instructing the parser to disable UTF-8 handling and assume plain 8 bit characters. By default, UTF-8 handling is enabled. Can be combined with other flags by use of | operator. <br/><br/>
 See <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function. </para><p/><h3 class="reference-header" id="namespacerapidxml_52e2c934ad9c845a5f4cc49570470556_152e2c934ad9c845a5f4cc49570470556">
				constant
			 parse_declaration_node</h3><h4>Synopsis</h4><code class="synopsis">const int parse_declaration_node
											  = 0x20;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Parse flag instructing the parser to create XML declaration node. By default, declaration node is not created. Can be combined with other flags by use of | operator. <br/><br/>
 See <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function. </para><p/><h3 class="reference-header" id="namespacerapidxml_0f7479dacbc868456d07897a8c072784_10f7479dacbc868456d07897a8c072784">
				constant
			 parse_comment_nodes</h3><h4>Synopsis</h4><code class="synopsis">const int parse_comment_nodes
											  = 0x40;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Parse flag instructing the parser to create comments nodes. By default, comment nodes are not created. Can be combined with other flags by use of | operator. <br/><br/>
 See <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function. </para><p/><h3 class="reference-header" id="namespacerapidxml_8e187746ba1ca04f107951ad32df962e_18e187746ba1ca04f107951ad32df962e">
				constant
			 parse_doctype_node</h3><h4>Synopsis</h4><code class="synopsis">const int parse_doctype_node
											  = 0x80;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Parse flag instructing the parser to create DOCTYPE node. By default, doctype node is not created. Although W3C specification allows at most one DOCTYPE node, RapidXml will silently accept documents with more than one. Can be combined with other flags by use of | operator. <br/><br/>
 See <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function. </para><p/><h3 class="reference-header" id="namespacerapidxml_1c20b2b2b75711cd76423e119c49f830_11c20b2b2b75711cd76423e119c49f830">
				constant
			 parse_pi_nodes</h3><h4>Synopsis</h4><code class="synopsis">const int parse_pi_nodes
											  = 0x100;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Parse flag instructing the parser to create PI nodes. By default, PI nodes are not created. Can be combined with other flags by use of | operator. <br/><br/>
 See <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function. </para><p/><h3 class="reference-header" id="namespacerapidxml_a5daff9d61c7d4eaf98e4d42efe628ee_1a5daff9d61c7d4eaf98e4d42efe628ee">
				constant
			 parse_validate_closing_tags</h3><h4>Synopsis</h4><code class="synopsis">const int parse_validate_closing_tags
											  = 0x200;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Parse flag instructing the parser to validate closing tag names. If not set, name inside closing tag is irrelevant to the parser. By default, closing tags are not validated. Can be combined with other flags by use of | operator. <br/><br/>
 See <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function. </para><p/><h3 class="reference-header" id="namespacerapidxml_ac1f06b1afd47b812732fb521b146fd9_1ac1f06b1afd47b812732fb521b146fd9">
				constant
			 parse_trim_whitespace</h3><h4>Synopsis</h4><code class="synopsis">const int parse_trim_whitespace
											  = 0x400;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Parse flag instructing the parser to trim all leading and trailing whitespace of data nodes. By default, whitespace is not trimmed. This flag does not cause the parser to modify source text. Can be combined with other flags by use of | operator. <br/><br/>
 See <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function. </para><p/><h3 class="reference-header" id="namespacerapidxml_88f95d4e275ba01408fefde83078651b_188f95d4e275ba01408fefde83078651b">
				constant
			 parse_normalize_whitespace</h3><h4>Synopsis</h4><code class="synopsis">const int parse_normalize_whitespace
											  = 0x800;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Parse flag instructing the parser to condense all whitespace runs of data nodes to a single space character. Trimming of leading and trailing whitespace of data is controlled by <a href="#namespacerapidxml_ac1f06b1afd47b812732fb521b146fd9_1ac1f06b1afd47b812732fb521b146fd9" kindref="member">rapidxml::parse_trim_whitespace</a> flag. By default, whitespace is not normalized. If this flag is specified, source text will be modified. Can be combined with other flags by use of | operator. <br/><br/>
 See <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function. </para><p/><h3 class="reference-header" id="namespacerapidxml_45751cf2f38fd6915f35b3122b46d5b6_145751cf2f38fd6915f35b3122b46d5b6">
				constant
			 parse_default</h3><h4>Synopsis</h4><code class="synopsis">const int parse_default
											  = 0;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Parse flags which represent default behaviour of the parser. This is always equal to 0, so that all other flags can be simply ored together. Normally there is no need to inconveniently disable flags by anding with their negated (~) values. This also means that meaning of each flag is a <i>negation</i> of the default setting. For example, if flag name is <a href="#namespacerapidxml_ccde57f6054857ee4042a1b4d98c83b9_1ccde57f6054857ee4042a1b4d98c83b9" kindref="member">rapidxml::parse_no_utf8</a>, it means that utf-8 is <i>enabled</i> by default, and using the flag will disable it. <br/><br/>
 See <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function. </para><p/><h3 class="reference-header" id="namespacerapidxml_a97ba1a0a79a6d66f4eef3612508d943_1a97ba1a0a79a6d66f4eef3612508d943">
				constant
			 parse_non_destructive</h3><h4>Synopsis</h4><code class="synopsis">const int parse_non_destructive
											  = parse_no_string_terminators | parse_no_entity_translation;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">A combination of parse flags that forbids any modifications of the source text. This also results in faster parsing. However, note that the following will occur: <ul><li><para>names and values of nodes will not be zero terminated, you have to use <a href="#classrapidxml_1_1xml__base_0dae694c8f7e4d89f1003e2f3a15a43c_10dae694c8f7e4d89f1003e2f3a15a43c" kindref="member">xml_base::name_size()</a> and <a href="#classrapidxml_1_1xml__base_aed5ae791b7164c1ee5e649198cbb3db_1aed5ae791b7164c1ee5e649198cbb3db" kindref="member">xml_base::value_size()</a> functions to determine where name and value ends </para></li><li><para>entities will not be translated </para></li><li><para>whitespace will not be normalized </para></li></ul>
See <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function. </para><p/><h3 class="reference-header" id="namespacerapidxml_398c5476e76102f8bd76c10bb0abbe10_1398c5476e76102f8bd76c10bb0abbe10">
				constant
			 parse_fastest</h3><h4>Synopsis</h4><code class="synopsis">const int parse_fastest
											  = parse_non_destructive | parse_no_data_nodes;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">A combination of parse flags resulting in fastest possible parsing, without sacrificing important data. <br/><br/>
 See <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function. </para><p/><h3 class="reference-header" id="namespacerapidxml_b4f2515265facb42291570307924bd57_1b4f2515265facb42291570307924bd57">
				constant
			 parse_full</h3><h4>Synopsis</h4><code class="synopsis">const int parse_full
											  = parse_declaration_node | parse_comment_nodes | parse_doctype_node | parse_pi_nodes | parse_validate_closing_tags;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">A combination of parse flags resulting in largest amount of data being extracted. This usually results in slowest parsing. <br/><br/>
 See <a href="#classrapidxml_1_1xml__document_8338ce6042e7b04d5a42144fb446b69c_18338ce6042e7b04d5a42144fb446b69c" kindref="member">xml_document::parse()</a> function. </para><p/><h3 class="reference-header" id="namespacerapidxml_b08b8d4293c203b69ed6c5ae77ac1907_1b08b8d4293c203b69ed6c5ae77ac1907">
				constant
			 print_no_indenting</h3><h4>Synopsis</h4><code class="synopsis">const int print_no_indenting
											  = 0x1;
									  </code><h4>Description</h4><para xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">Printer flag instructing the printer to suppress indenting of XML. See <a href="#namespacerapidxml_b94d570fc4c4ab2423813cd0243326b1_1b94d570fc4c4ab2423813cd0243326b1" kindref="member">print()</a> function. </para><p/></body></html>